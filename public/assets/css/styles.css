@charset "UTF-8"; /* This must be at the very top if used */
@import url("styles.css"); /* All @import rules must follow @charset, if defined */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    transition: transform 0.3s ease;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
    color: #fff;
    transform: scale(1.1); /* Slight grow effect */
}

/* responsive navbar css */
@media screen and (max-width: 600px) {
    nav .sideMenuButton {
        display: flex;
    }
    nav .navbar {
        display: none;
    }
    .sideNavigationBar {
        display: block !important;
    }
}

.sideNavigationBar {
    height: 100%;
    position: fixed;
    top: 0;
    right: 0%;
    background-color: green;
    overflow-x: hidden;
    transition: 0.3s ease-in;
    padding-top: 60px;
    display: none;
}

.sideNavigationBar a {
    padding: 8px 8px 8px 40px;
    display: block;
    font-size: 25px;
    font-weight: 500;
    color: #d1d1d1;
    transition: 0.3s;
    text-decoration: none;
}

.sideNavigationBar a button {
    padding: 10px 20px;
    border-radius: 10px;
    color: green;
    font-size: 16px;
    border-style: none;
    font-weight: 700;
}

.sideNavigationBar a:hover {
    color: white;
}

.sideNavigationBar .closeButton {
    position: absolute;
    top: 10px;
    right: 25px;
    font-size: 20px;
    margin-left: 50px;
}

.active {
    background-color: #717171;
}

/* Fading animation */
.fade {
    animation-name: fade;
    animation-duration: 1.5s;
}

@keyframes fade {
    from { opacity: .4; }
    to { opacity: 1; }
}

/* On smaller screens, decrease text size */
@media only screen and (max-width: 300px) {
    .text {
        font-size: 11px;
    }
}

a {
    text-decoration: none;
    color: black;
}

/* Header content responsive */
@media screen and (max-width: 980px) {
    .side2 img {
        width: 350px;
    }
    .side1 h1 {
        font-size: 40px;
    }
    .side1 p {
        font-size: 17px;
    }
    .line {
        margin-top: 80px;
    }
}

@media screen and (max-width: 600px) {
    .side1 h1 {
        font-size: 35px;
    }
    .line {
        flex-direction: column;
    }
    .line .side1 {
        padding: 30px;
    }
}

/* STUDENT DASHBOARD */
/* Sidebar Styles */
.sidebar {
    width: 250px; /* Sidebar width */
    background-color: #ffffff; /* White background */
    padding: 20px; /* Internal padding */
    height: calc(100vh - 80px); /* Full height minus navbar height */
    position: fixed; /* Keep it fixed on the side */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Shadow for depth */
    transition: background-color 0.3s; /* Smooth background color transition */
    overflow-y: auto; /* Allow scrolling if content exceeds height */
    display: block; /* Ensure it's displayed */
}

/* Sidebar Links */
.sidebar a {
    display: block;
    padding: 12px; /* Padding for better touch target */
    color: #333; /* Dark text color for visibility */
    text-decoration: none; /* Remove underline */
    margin-bottom: 15px; /* Spacing between links */
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s, color 0.3s; /* Smooth transition effects */
}

/* Sidebar Links Hover Effect */
.sidebar a:hover {
    background-color: orange; /* Background color change on hover */
    color: #fff; /* Change text color on hover for better contrast */
}

.main {
    margin-left: 200px; /* Same as the width of the sidenav */
    padding: 0px 10px;
}

@media screen and (max-height: 450px) {
    .sidebar {padding-top: 15px;}
    .sidebar a {font-size: 18px;}
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.top-left-btn {
    position: absolute;
    top: 200px;
    left: 250px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: gainsboro;
    color: black;
    border: none;
    cursor: pointer;
}

.top-right-btn {
    position: absolute;
    top: 200px;
    right: 80px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: gainsboro;
    color: black;
    border: none;
    cursor: pointer;
}

.content-box {
    margin-left: 270px; /* Adjust for sidebar */
    padding: 20px;
    background-color: #e3f2fd; /* Light background */
    min-height: calc(100vh - 150px); /* Height adjusted for header and footer */
}

.button-group {
    margin-bottom: 20px;
}

.button-group button {
    padding: 10px 20px;
    margin: 5px;
    font-size: 16px;
    cursor: pointer;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
}

.chart-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    display: none;
}

/* Additional styles */
#yt_logo img {
    width: 120px;
    height: 100px;
}

.box p {
    font-size: 0.8em; /* Adjust the size as needed */
}

.slideshow-container img {
    width: 100%;
}

.dot {
    height: 15px;
    width: 15px;
    margin: 0 2px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    transition: background-color 0.6s ease;
}

.active {
    background-color: #717171;
}

.fade {
    -webkit-animation-name: fade;
    -webkit-animation-duration: 1.5s;
    animation-name: fade;
    animation-duration: 1.5s;
}

@-webkit-keyframes fade {
    from {opacity: .4} 
    to {opacity: 1}
}

@keyframes fade {
    from {opacity: .4} 
    to {opacity: 1}
}

nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    transition: transform 0.3s ease;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
    color: #fff;
    transform: scale(1.1); /* Slight grow effect */
}

.sideNavigationBar {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #111;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
}

.sideNavigationBar a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sideNavigationBar a:hover {
    color: #f1f1f1;
}

.sideNavigationBar .closeButton {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}

/* Login form styling */
.login-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 160px);
}

.login-container {
    background-color: #f2f2f2;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 300px;
    text-align: center;
}

.login-container h2 {
    margin-bottom: 20px;
}

.login-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.login-container input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.login-container button {
    width: 100%;
    padding: 10px;
    background-color: goldenrod;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.login-container button:hover {
    background-color: darkgoldenrod;
}

xfooter {
    background-color: #2d3748;
    color: #ecf0f1;
    padding: 40px 5%;
    text-align: center;
    position: fixed; /* Ensure footer is relative for fixed positioning */
    bottom: 0; /* Stick to bottom */
    width: 100%; /* Full width */
}

xfooter a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
}

xfooter a:hover {
    text-decoration: underline;
}

/* Dashboard styles */
.container {
    display: flex;
}



.main-content {
    margin-left: 220px;
    padding: 20px;
    width: calc(100% - 220px);
}

.stats {
    display: flex;
    gap: 20px;
}

.stat-box {
    background-color: #f4f4f4;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    text-align: center;
}

.stat-box h3 {
    margin-bottom: 10px;
}


/* Registration form styling */
.main-content {
    margin: 20px;
}

form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

label {
    font-weight: bold;
}

input, select, button {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

button {
    background-color: goldenrod;
    color: white;
    cursor: pointer;
}

button:hover {
    background-color: darkgoldenrod;
}


/* Admin dashboard styling */
.main-content {
    margin: 20px;
}

form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

label {
    font-weight: bold;
}

input, select, button {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

button {
    background-color: goldenrod;
    color: white;
    cursor: pointer;
}

button:hover {
    background-color: darkgoldenrod;
}






    .top-right-username {
        position: absolute;
        top: 10px;
        right: 10px;
        font-weight: bold;
    }

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    background-image: url("img/background.webp"); /* Specify the background image */
    color: black;
    font-family: "Fira Sans", sans-serif;
    background-color: white;
}

nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    transition: transform 0.3s ease;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
    color: #fff;
    transform: scale(1.1); /* Slight grow effect */
}

nav .sideMenuButton {
    font-size: 30px;
    font-weight: bolder;
    cursor: pointer;
    display: none;
}

/* responsive navbar css */
@media screen and (max-width: 600px) {
    nav .sideMenuButton {
        display: flex;
    }
    nav .navbar {
        display: none;
    }
    .sideNavigationBar {
        display: block !important;
    }
}

.sideNavigationBar {
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    background-color: green;
    overflow-x: hidden;
    transition: 0.3s ease-in;
    padding-top: 60px;
    display: none;
}

.sideNavigationBar a {
    padding: 8px 8px 8px 40px;
    display: block;
    font-size: 25px;
    font-weight: 500;
    color: #d1d1d1;
    transition: 0.3s;
    text-decoration: none;
}

.sideNavigationBar a:hover {
    color: white;
}

.sideNavigationBar .closeButton {
    position: absolute;
    top: 10px;
    right: 25px;
    font-size: 20px;
    margin-left: 50px;
}

.mySlides {
    display: none;
}

img {
    vertical-align: middle;
}

/* Slideshow container */
.slideshow-container {
    max-width: 1000px;
    position: relative;
    margin: auto;
}

/* Caption text */
.text {
    color: #f2f2f2;
    font-size: 15px;
    padding: 8px 12px;
    position: absolute;
    bottom: 8px;
    width: 100%;
    text-align: center;
}

/* Number text (1/3 etc) */
.numbertext {
    color: #f2f2f2;
    font-size: 12px;
    padding: 8px 12px;
    position: absolute;
    top: 0;
}

/* The dots/bullets/indicators */
.dot {
    height: 15px;
    width: 15px;
    margin: 0 2px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    transition: background-color 0.6s ease;
}

.active {
    background-color: #717171;
}

/* Fading animation */
.fade {
    animation-name: fade;
    animation-duration: 1.5s;
}

@keyframes fade {
    from { opacity: .4; }
    to { opacity: 1; }
}

/* On smaller screens, decrease text size */
@media only screen and (max-width: 300px) {
    .text {
        font-size: 11px;
    }
}

a {
    text-decoration: none;
    color: black;
}

/* Header content responsive */
@media screen and (max-width: 980px) {
    .side2 img {
        width: 350px;
    }
    .side1 h1 {
        font-size: 40px;
    }
    .side1 p {
        font-size: 17px;
    }
    .line {
        width: 300px;
    }
}

.content {
    display: flex;
    justify-content: center; /* Center the content */
    margin: 20px;
}

.boxes {
    display: flex;
    justify-content: space-between; /* Space between columns */
    width: 100%; /* Make sure it takes full width */
    max-width: 1200px; /* Restrict the width to avoid too much stretching */
}

.box {
    flex: 1; /* Make each box take equal space */
    padding: 10px;
    margin: 5px;
    background-color: #f0f0f0; /* Light background for visibility */
    border-radius: 8px; /* Add rounded corners */
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); /* Add subtle shadow */
}

p {
    font-size: 16px;
    line-height: 1.6;
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    background-image: url("img/background.webp"); /* Specify the background image */
    color: black;
    font-family: "Fira Sans", sans-serif;
    background-color: white;
    padding-bottom: 60px;
}
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    transition: transform 0.3s ease;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
    color: #fff;
    transform: scale(1.1); /* Slight grow effect */
}

nav .sideMenuButton {
    font-size: 30px;
    font-weight: bolder;
    cursor: pointer;
    display: none;
}

/* responsive navbar css */
@media screen and (max-width: 600px) {
    nav .sideMenuButton {
        display: flex;
    }
    nav .navbar {
        display: none;
    }
    .sideNavigationBar {
        display: block !important;
    }
}



.container {
    display: flex;
}



.main {
    margin-left: 200px; /* Same as the width of the sidenav */
    padding: 0px 10px;
}

.main-content {
    margin-left: 220px;
    padding: 20px;
    width: calc(100% - 220px);
}

@media screen and (max-height: 450px) {
    .sidebar {padding-top: 15px;}
    .sidebar a {font-size: 18px;}
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.top-left-btn {
    position: absolute;
    top: 200px;
    left: 250px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: gainsboro;
    color: black;
    border: none;
    cursor: pointer;
}

.top-right-btn {
    position: absolute;
    top: 200px;
    right: 80px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: gainsboro;
    color: black;
    border: none;
    cursor: pointer;
}

.button-group {
    margin-bottom: 20px;
}

.button-group button {
    padding: 10px 20px;
    margin: 5px;
    font-size: 16px;
    cursor: pointer;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
}

.chart-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    display: none;
}

.top {
    text-align: center;
}

.stats {
    display: flex;
    gap: 20px;
}

.stat-box {
    background-color: #f4f4f4;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    text-align: center;
}

.stat-box h3 {
    margin-bottom: 10px;
}


.student-dashboard-sidebar {
    height: 250px; /* Reduced height */
    overflow-y: scroll; /* Always show scrollbar */
}





.student-dashboard-sidebar ul {
  list-style: none;
  padding: 0;
}
.popup {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 97%;
    height: 95%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.popup-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.disabled {
    pointer-events: none;
    color: gray;
    cursor: not-allowed;
}

.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top of other elements */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: hidden; /* Hide overflow */
    background-color: rgba(0, 0, 0, 0.8); /* Black w/ opacity */
}

.modal-content {
    background-color: #fefefe;
    margin: 0; /* Remove margin */
    padding: 0; /* Remove padding */
    border: none; /* Remove border */
    width: 100%; /* Full width */
    height: 100%; /* Full height */
}

iframe {
    width: 100%;
    height: 100%;
    border: none; /* Remove border for the iframe */
}

/* Add these styles */
.slideshow-container {
    max-width: 1000px;
    position: relative;
    margin: auto;
    overflow: hidden;
}

.mySlides {
    position: relative;
}

.text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 15px;
    border-radius: 5px;
    width: 80%;
    z-index: 2;
}

.numbertext {
    position: absolute;
    top: 20px;
    left: 20px;
    color: white;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 8px;
    border-radius: 5px;
    z-index: 2;
}

.mySlides img {
    width: 100%;
    height: auto;
    display: block;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    height: 80%;
}

#studentInfoFrame {
    width: 100%;
    height: 100%;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.container {
    margin-top: 120px;
    padding: 20px;
}

.main-content {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

form {
    display: grid;
    gap: 15px;
}

label {
    font-weight: bold;
    margin-bottom: 5px;
}

input, select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button[type="submit"] {
    background: #004AAD;
    color: white;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 20px;
}

button[type="submit"]:hover {
    background: #003c8a;
}

.btn {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    background-color: #45a049;
}

.dashboard-actions {
    margin: 20px 0;
}

.search-bar input[type="text"] {
    padding: 8px;
    width: 300px;
    margin-right: 10px;
}
