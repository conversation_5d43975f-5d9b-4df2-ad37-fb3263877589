@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
}

/* responsive navbar css */
@media screen and (max-width: 600px) {
    nav .sideMenuButton {
        display: flex;
    }
    nav .navbar {
        display: none;
    }
    .sideNavigationBar {
        display: block !important;
    }
}

/* Sidebar Styles */
.sidebar {
    width: 250px; /* Sidebar width */
    background-color: #ffffff; /* White background */
    padding: 20px; /* Internal padding */
    height: calc(100vh - 80px); /* Full height minus navbar height */
    position: fixed; /* Keep it fixed on the side */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Shadow for depth */
    transition: background-color 0.3s; /* Smooth background color transition */
    overflow-y: auto; /* Allow scrolling if content exceeds height */
    display: block; /* Ensure it's displayed */
}

/* Sidebar Links */
.sidebar a {
    display: block;
    padding: 12px; /* Padding for better touch target */
    color: #333; /* Dark text color for visibility */
    text-decoration: none; /* Remove underline */
    margin-bottom: 15px; /* Spacing between links */
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s, color 0.3s; /* Smooth transition effects */
}

/* Sidebar Links Hover Effect */
.sidebar a:hover {
    background-color: orange; /* Background color change on hover */
    color: #fff; /* Change text color on hover for better contrast */
}

.sideNavigationBar {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #111;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
}

.sideNavigationBar a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sideNavigationBar a:hover {
    color: #f1f1f1;
}

.sideNavigationBar .closeButton {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}



