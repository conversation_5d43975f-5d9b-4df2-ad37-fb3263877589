@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
}

/* Sidebar Styles */
.sidebar {
    width: 250px; /* Sidebar width */
    background-color: #ffffff; /* White background */
    padding: 20px; /* Internal padding */
    height: calc(100vh - 80px); /* Full height minus navbar height */
    position: fixed; /* Keep it fixed on the side */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Shadow for depth */
    transition: background-color 0.3s; /* Smooth background color transition */
    overflow-y: auto; /* Allow scrolling if content exceeds height */
    display: block; /* Ensure it's displayed */
}

/* Sidebar Links */
.sidebar a {
    display: block;
    padding: 12px; /* Padding for better touch target */
    color: #333; /* Dark text color for visibility */
    text-decoration: none; /* Remove underline */
    margin-bottom: 15px; /* Spacing between links */
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s, color 0.3s; /* Smooth transition effects */
}

/* Sidebar Links Hover Effect */
.sidebar a:hover {
    background-color: orange; /* Background color change on hover */
    color: #fff; /* Change text color on hover for better contrast */
}

/* Main Content Styles */
.main-content {
    margin-left: 270px; /* Adjust margin to accommodate sidebar */
    padding: 20px; /* Add padding */
    display: flex;
    flex-direction: column; /* Stack items vertically */
    align-items: center; /* Center align the content */
    justify-content: flex-start; /* Align items to the top */
}

/* Info Box Styles */
.info-box {
    background-color: #f9f9f9; /* Light background for contrast */
    padding: 20px; /* Padding for internal spacing */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    cursor: pointer; /* Cursor changes to pointer on hover */
    transition: background-color 0.3s; /* Smooth background color transition */
    width: 100%; /* Full width of the container */
    max-width: 600px; /* Maximum width for the info box */
    margin-top: 20px; /* Margin above the box */
}

.info-box:hover {
    background-color: #f1f1f1; /* Slightly darker on hover */
}

/* Footer */
footer {
    background-color: #2d3748;
    color: #ecf0f1;
    padding: 40px 5%;
    text-align: center;
    position: fixed; /* Ensure footer is relative for fixed positioning */
    bottom: 0; /* Stick to bottom */
    width: 100%; /* Full width */
}

footer a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
}

footer a:hover {
    text-decoration: underline;
}

