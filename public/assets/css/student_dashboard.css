@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

/* Ensure proper stacking context */
.sidebar {
    width: 250px;
    height: calc(100vh - 250px); /* Shorter height */
    background-color: #ffffff;
    padding: 20px;
    position: fixed;
    top: 60px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    margin-bottom: 60px;
    z-index: 2000;
}

.dropdown {
    position: relative;
    z-index: 3000;
}

.dropdown-menu {
    position: absolute;
    left: 0;
    top: 100%;
    background: white;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    z-index: 3000;
}

.content-box {
    z-index: 1;
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
}

/* responsive navbar css */
@media screen and (max-width: 600px) {
    nav .sideMenuButton {
        display: flex;
    }
    nav .navbar {
        display: none;
    }
    .sideNavigationBar {
        display: block !important;
    }
}



.sideNavigationBar {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #111;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
}

.sideNavigationBar a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sideNavigationBar a:hover {
    color: #f1f1f1;
}

.sideNavigationBar .closeButton {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}


















/* Media Queries for Responsive Design */
@media (max-width: 768px) {
    .navbar ul {
        flex-direction: column; /* Stack nav items on smaller screens */
        align-items: center; /* Center align */
    }

    .main-content {
        margin-left: 0; /* Adjust margin for smaller screens */
    }

    .sidebar {
        width: 100%; /* Full width for smaller screens */
        height: auto; /* Auto height for content */
    }
}
