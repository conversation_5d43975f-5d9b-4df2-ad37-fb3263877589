@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    transition: transform 0.3s ease;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
    color: #fff;
    transform: scale(1.1); /* Slight grow effect */
}

/* Slideshow */
.slideshow-container {
    position: relative;
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
}

.mySlides img {
    width: 100%;
    height: 500px;
    object-fit: cover;
}

.numbertext, .text {
    position: absolute;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 10px;
    border-radius: 5px;
}

.numbertext {
    top: 10px;
    left: 10px;
}

.text {
    bottom: 20px;
    left: 20px;
}

/* Arrows */
.prev, .next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    width: auto;
    padding: 16px;
    margin-top: -22px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    background-color: rgba(0,0,0,0.6);
    border-radius: 0 3px 3px 0;
    user-select: none;
    transition: 0.6s ease;
}

.next {
    right: 0;
    border-radius: 3px 0 0 3px;
}

.prev:hover, .next:hover {
    background-color: rgba(0,0,0,0.8);
}

/* About Section */
.about {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 60px 0;
}

.about h2 {
    font-size: 24px;
    color: #A52A2A;
    margin-bottom: 20px;
    text-align: center;
}

.about p {
    font-size: 16px;
    color: #555;
    text-align: center;
}

/* Boxes */
.boxes {
    display: flex;
    justify-content: space-around;
    gap: 20px;
    padding: 20px 5%;
    flex-wrap: wrap;
}

.box {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 250px;
    text-align: center;
}

.box h2 {
    font-size: 20px;
    color: #8B1A1A;
    margin-bottom: 10px;
}

.box p {
    font-size: 14px;
    color: #555;
}

/* Footer */
footer {
    background-color: #2d3748;
    color: #ecf0f1;
    padding: 40px 5%;
    text-align: center;
}

footer a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
}

footer a:hover {
    text-decoration: underline;
}
