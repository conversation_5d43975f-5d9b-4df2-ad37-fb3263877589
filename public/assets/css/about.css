@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    transition: transform 0.3s ease;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
    color: #fff;
    transform: scale(1.1); /* Slight grow effect */
}

/* Section Styles */
.content-section {
    padding: 40px 20px;
    background-color: #ffffff; /* White background for content */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    border-radius: 8px; /* Rounded corners */
    margin: 20px; /* Space around sections */
}

.abouthead {
    font-size: 28px;
    text-align: center;
    color: #2c3e50; /* Darker heading color */
    margin-bottom: 20px; /* Spacing below heading */
}

.about {
    font-size: 16px; /* Text size for the paragraph */
    line-height: 1.5; /* Better readability */
}

/* Steps Section */
.steps-section {
    padding: 40px 20px;
    background-color: #ecf0f1; /* Light gray background */
    margin: 20px; /* Space around sections */
    border-radius: 8px; /* Rounded corners */
}

.steps-section h2 {
    text-align: center;
    color: #2c3e50; /* Darker heading color */
}

.slider-container {
    display: flex;
    align-items: center;
    justify-content: center; /* Center the slider */
    margin-top: 20px; /* Spacing above the slider */
}

.steps-section {
    position: relative; /* Set position relative for absolute positioning of buttons */
}

.slider-arrow {
    position: absolute; /* Change to absolute positioning */
    top: 50%; /* Center the buttons vertically within the section */
    transform: translateY(-50%); /* Adjust vertical alignment */
    background-color: #3498db; /* Button background color */
    border: none;
    color: white;
    padding: 10px;
    cursor: pointer;
    border-radius: 4px; /* Rounded corners for buttons */
    transition: background-color 0.3s; /* Transition effect */
    z-index: 10; /* Ensure buttons are above other content */
}

.left-arrow {
    left: 20px; /* Fixed position on the left */
}

.right-arrow {
    right: 20px; /* Fixed position on the right */
}

.slider-arrow:hover {
    background-color: #2980b9; /* Darker blue on hover */
}

/*contact Section*/

.contact-section {
    padding: 20px; /* Add some padding to the section */
    background-color: #f9f9f9; /* Optional: Light background for the contact section */
}

.contact-section h2 {
    text-align: center;
}

.contact-info {
    display: flex; /* Use flexbox for even spacing */
    justify-content: center; /* Evenly space items */
    flex-wrap: wrap; /* Allow items to wrap to the next line if necessary */
    gap: 30px;
} 

.contact-item {
    flex: 1; /* Make each item take equal space */
    margin: 10px; /* Add margin for spacing between items */
    text-align: center; /* Center-align text in each contact item */
    text-align: center;
    padding: 15px;
    background-color: #f1f1f1;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.contact-item:hover {
    transform: scale(1.05);
}

.contact-item i {
    font-size: 2rem;
    color: #daa520;
}


.contact-item p {
    font-weight: bold; /* Optional: Make the label bold */
    font-weight: 600;
    margin-top: 10px;
    font-size: 1.1rem;
}

.contact-item span, .contact-item a {
    display: block; /* Stack the text and links */
    margin-top: 5px; /* Space between label and text */
    font-size: 0.9rem;
    color: #777;
}

/* Footer */
footer {
    background-color: #2d3748;
    color: #ecf0f1;
    padding: 40px 5%;
    text-align: center;
    position: fixed; /* Ensure footer is relative for fixed positioning */
    bottom: 0; /* Stick to bottom */
}

footer a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
}

footer a:hover {
    text-decoration: underline;
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
    .navbar ul {
        flex-direction: column; /* Stack nav items on smaller screens */
        align-items: center; /* Center align */
    }

    .contact-info {
        flex-direction: column; /* Stack contact items on smaller screens */
    }
}
