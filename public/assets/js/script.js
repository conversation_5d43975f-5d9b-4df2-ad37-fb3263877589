let slideIndex = 1;
showSlides(slideIndex);

// Automatic slide every 3 seconds
let slideInterval = setInterval(() => plusSlides(1), 3000);

function plusSlides(n) {
    clearInterval(slideInterval);  // Stop automatic sliding when manually navigating
    showSlides(slideIndex += n);
    slideInterval = setInterval(() => plusSlides(1), 8000); // Resume automatic sliding
}

function showSlides(n) {
    let i;
    let slides = document.getElementsByClassName("mySlides");
    
    if (n > slides.length) {slideIndex = 1}    
    if (n < 1) {slideIndex = slides.length}
    
    for (i = 0; i < slides.length; i++) {
        slides[i].style.display = "none";  
    }
    
    slides[slideIndex - 1].style.display = "block";  
}


document.getElementById('myInfoBtn').addEventListener('click', function() {
    fetch('fetch_student_info.php')
        .then(response => response.json())
        .then(data => {
            const infoHtml = `
                <div class="student-info">
                    <h3>Student Details</h3>
                    <p><strong>Student ID:</strong> ${data.student_id}</p>
                    <p><strong>Name:</strong> ${data.first_name} ${data.last_name}</p>
                    <p><strong>Gender:</strong> ${data.gender}</p>
                    <p><strong>Date of Birth:</strong> ${data.dob}</p>
                    <p><strong>Email:</strong> ${data.student_email}</p>
                    <p><strong>Phone:</strong> ${data.phone_number}</p>
                    <p><strong>Address:</strong> ${data.residential_address}</p>
                    <p><strong>Home Province:</strong> ${data.home_province}</p>
                    <p><strong>Program:</strong> ${data.program}</p>
                    
                    <h3>Guardian Information</h3>
                    <p><strong>Guardian Name:</strong> ${data.guardian_name}</p>
                    <p><strong>Guardian Occupation:</strong> ${data.guardian_occupation}</p>
                    <p><strong>Guardian Phone:</strong> ${data.guardian_phone_number}</p>
                    <p><strong>Guardian Email:</strong> ${data.guardian_email}</p>
                </div>
            `;
            
            document.getElementById('studentInfoDisplay').innerHTML = infoHtml;
        });
});


function openStudentInfo() {
    document.getElementById('studentInfoModal').style.display = 'block';
}

document.getElementsByClassName('close')[0].onclick = function() {
    document.getElementById('studentInfoModal').style.display = 'none';
}

window.onclick = function(event) {
    if (event.target == document.getElementById('studentInfoModal')) {
        document.getElementById('studentInfoModal').style.display = 'none';
    }
}
