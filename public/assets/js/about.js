// ABOUT PAGE //


// Array of registration steps
const steps = [
    "Login to the DBTI Online Registration website to get started with your course registration.",
    "Fill in your personal details as prompted in the registration form.",
    "Select your courses for the upcoming semester.",
    "Review your information and submit the form to complete registration.",
];

// Set the current step index
let currentStep = 0;

// Function to update the displayed content
function updateSliderContent() {
    document.getElementById("slider-content").innerHTML = `<p>${steps[currentStep]}</p>`;
}

// Function to navigate to the next step
function slideRight() {
    // Check if it's the last step, loop back to the first step
    if (currentStep < steps.length - 1) {
        currentStep++;
    } else {
        currentStep = 0; // Loop back to the beginning
    }
    updateSliderContent();
}

// Function to navigate to the previous step
function slideLeft() {
    // Check if it's the first step, loop back to the last step
    if (currentStep > 0) {
        currentStep--;
    } else {
        currentStep = steps.length - 1; // Loop back to the end
    }
    updateSliderContent();
}

// Initialize with the first step content
updateSliderContent();





