<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once __DIR__ . '/../config.php';

// Check if user is admin or cashier
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'cashier')) {
    header("Location: ../login.php");
    exit();
}

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file path
$log_file = $logs_dir . 'stripe_webhook_test.log';

// Initialize variables
$message = '';
$error = '';
$log_content = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_webhook']) || isset($_POST['test_debug_webhook']) || isset($_POST['test_simple_webhook'])) {
        // Test the webhook endpoint
        $webhook_url = isset($_POST['webhook_url']) ? $_POST['webhook_url'] : '';

        // If testing debug webhook, modify the URL
        if (isset($_POST['test_debug_webhook'])) {
            // Replace webhook.php with webhook_debug.php in the URL
            $webhook_url = preg_replace('/webhook\.php$/', 'webhook_debug.php', $webhook_url);
            if (strpos($webhook_url, 'webhook_debug.php') === false) {
                // If the URL doesn't end with webhook.php, append webhook_debug.php
                $webhook_url = rtrim($webhook_url, '/') . '/webhook_debug.php';
            }
        }

        // If testing simple webhook, modify the URL
        if (isset($_POST['test_simple_webhook'])) {
            // Replace webhook.php with webhook_simple.php in the URL
            $webhook_url = preg_replace('/webhook\.php$/', 'webhook_simple.php', $webhook_url);
            if (strpos($webhook_url, 'webhook_simple.php') === false) {
                // If the URL doesn't end with webhook.php, append webhook_simple.php
                $webhook_url = rtrim($webhook_url, '/') . '/webhook_simple.php';
            }
        }

        if (empty($webhook_url)) {
            $error = "Please enter a webhook URL";
        } else {
            try {
                // Create a test payload
                $payload = [
                    'id' => 'evt_' . bin2hex(random_bytes(16)),
                    'type' => 'checkout.session.completed',
                    'created' => time(),
                    'data' => [
                        'object' => [
                            'id' => 'cs_test_' . bin2hex(random_bytes(16)),
                            'client_reference_id' => 'TEST_STUDENT_ID',
                            'amount_total' => 10000, // $100.00
                            'payment_intent' => 'pi_test_' . bin2hex(random_bytes(16))
                        ]
                    ],
                    'test' => true
                ];

                // Initialize cURL
                $ch = curl_init($webhook_url);

                // Set cURL options
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Stripe-Signature: t=' . time() . ',v1=test_signature'
                ]);

                // Execute cURL request
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                // Check for errors
                if (curl_errno($ch)) {
                    throw new Exception(curl_error($ch));
                }

                // Close cURL
                curl_close($ch);

                // Log the test
                file_put_contents($log_file,
                    date('Y-m-d H:i:s') . ' - Manual webhook test sent to: ' . $webhook_url . PHP_EOL .
                    date('Y-m-d H:i:s') . ' - Payload: ' . json_encode($payload) . PHP_EOL .
                    date('Y-m-d H:i:s') . ' - Response code: ' . $http_code . PHP_EOL .
                    date('Y-m-d H:i:s') . ' - Response: ' . $response . PHP_EOL . PHP_EOL,
                    FILE_APPEND);

                if (isset($_POST['test_debug_webhook'])) {
                    $message = "Debug webhook sent successfully! Response code: $http_code. Check the logs for detailed information.";

                    // Automatically check for debug logs
                    $debug_log_file = $logs_dir . 'stripe_webhook_debug.log';
                    if (file_exists($debug_log_file)) {
                        $debug_log_content = file_get_contents($debug_log_file);
                        $log_content = "Debug Webhook Log:\n\n" . $debug_log_content;
                    }
                } else if (isset($_POST['test_simple_webhook'])) {
                    $message = "Simple webhook sent successfully! Response code: $http_code. Check the logs for detailed information.";

                    // Automatically check for simple webhook logs
                    $simple_log_file = $logs_dir . 'stripe_webhook_simple.log';
                    if (file_exists($simple_log_file)) {
                        $simple_log_content = file_get_contents($simple_log_file);
                        $log_content = "Simple Webhook Log:\n\n" . $simple_log_content;

                        // Also check for pending payments
                        $pending_file = $logs_dir . 'pending_payments.json';
                        if (file_exists($pending_file)) {
                            $pending_content = file_get_contents($pending_file);
                            if (!empty($pending_content)) {
                                $log_content .= "\n\nPending Payments:\n\n" . $pending_content;
                            }
                        }
                    }
                } else {
                    $message = "Test webhook sent successfully! Response code: $http_code";
                }
            } catch (Exception $e) {
                $error = "Error sending test webhook: " . $e->getMessage();
                file_put_contents($log_file,
                    date('Y-m-d H:i:s') . ' - Error sending test webhook: ' . $e->getMessage() . PHP_EOL,
                    FILE_APPEND);
            }
        }
    } elseif (isset($_POST['view_logs'])) {
        // View the logs
        $log_content = "";
        $logs_found = false;

        // Check for main test logs
        if (file_exists($log_file)) {
            $log_content = "=== WEBHOOK TEST LOGS ===\n\n" . file_get_contents($log_file);
            $logs_found = true;
        }

        // Check for debug logs
        $debug_log_file = $logs_dir . 'stripe_webhook_debug.log';
        if (file_exists($debug_log_file)) {
            $debug_log_content = file_get_contents($debug_log_file);
            if (!empty($debug_log_content)) {
                $log_content .= "\n\n=== DEBUG WEBHOOK LOGS ===\n\n" . $debug_log_content;
                $logs_found = true;
            }
        }

        // Check for simple webhook logs
        $simple_log_file = $logs_dir . 'stripe_webhook_simple.log';
        if (file_exists($simple_log_file)) {
            $simple_log_content = file_get_contents($simple_log_file);
            if (!empty($simple_log_content)) {
                $log_content .= "\n\n=== SIMPLE WEBHOOK LOGS ===\n\n" . $simple_log_content;
                $logs_found = true;
            }
        }

        // Check for pending payments
        $pending_file = $logs_dir . 'pending_payments.json';
        if (file_exists($pending_file)) {
            $pending_content = file_get_contents($pending_file);
            if (!empty($pending_content)) {
                $log_content .= "\n\n=== PENDING PAYMENTS ===\n\n" . $pending_content;
                $logs_found = true;
            }
        }

        // Check for main webhook logs
        $webhook_log_file = $logs_dir . 'stripe_webhook.log';
        if (file_exists($webhook_log_file)) {
            $webhook_log_content = file_get_contents($webhook_log_file);
            if (!empty($webhook_log_content)) {
                $log_content .= "\n\n=== MAIN WEBHOOK LOGS ===\n\n" . $webhook_log_content;
                $logs_found = true;
            }
        }

        if (!$logs_found) {
            $log_content = "No logs found.";
        }
    } elseif (isset($_POST['clear_logs'])) {
        // Clear the logs
        $log_files = [
            $log_file,
            $logs_dir . 'stripe_webhook_debug.log',
            $logs_dir . 'stripe_webhook_simple.log',
            $logs_dir . 'stripe_webhook.log'
        ];

        foreach ($log_files as $file) {
            if (file_exists($file)) {
                file_put_contents($file, '');
            }
        }

        // Also clear pending payments if requested
        if (isset($_POST['clear_pending']) && file_exists($logs_dir . 'pending_payments.json')) {
            file_put_contents($logs_dir . 'pending_payments.json', '[]');
        }

        $message = "Logs cleared successfully.";
    }
}

// Get the current domain for default webhook URL
$domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'dbtionline.waghitech.com';
$default_webhook_url = "https://$domain/stripe/webhook.php";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stripe Webhook Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        form {
            margin-top: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #ffbf00;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 10px;
        }
        button:hover {
            background-color: #e6ac00;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #666;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab-button {
            padding: 10px 20px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab-button.active {
            background-color: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-bolt"></i> Stripe Webhook Test</h1>

        <?php if (!empty($message)): ?>
            <div class="alert alert-success">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <div class="tab-container">
            <div class="tab-buttons">
                <div class="tab-button active" onclick="openTab(event, 'test-tab')">Test Webhook</div>
                <div class="tab-button" onclick="openTab(event, 'logs-tab')">View Logs</div>
            </div>

            <div id="test-tab" class="tab-content active">
                <p>
                    Use this tool to test if your webhook endpoint is accessible and functioning correctly.
                    This will send a test webhook payload to your endpoint.
                </p>

                <form method="post" action="">
                    <label for="webhook_url">Webhook URL:</label>
                    <input type="text" id="webhook_url" name="webhook_url" value="<?php echo $default_webhook_url; ?>" placeholder="https://example.com/stripe/webhook.php">

                    <div class="button-group">
                        <button type="submit" name="test_webhook">
                            <i class="fas fa-paper-plane"></i> Send Test Webhook
                        </button>

                        <button type="submit" name="test_debug_webhook" style="background-color: #4CAF50;">
                            <i class="fas fa-bug"></i> Test Debug Webhook
                        </button>

                        <button type="submit" name="test_simple_webhook" style="background-color: #2196F3;">
                            <i class="fas fa-feather"></i> Test Simple Webhook
                        </button>
                    </div>

                    <p><small>Use the "Test Debug Webhook" button to send a test to the debug endpoint that will provide more detailed error information. The "Test Simple Webhook" button uses a simplified handler that doesn't require database connection.</small></p>
                </form>
            </div>

            <div id="logs-tab" class="tab-content">
                <p>
                    View the logs of webhook test attempts and responses.
                </p>

                <form method="post" action="">
                    <div class="button-group">
                        <button type="submit" name="view_logs">
                            <i class="fas fa-sync"></i> Refresh Logs
                        </button>

                        <button type="submit" name="clear_logs" onclick="return confirm('Are you sure you want to clear all logs?');">
                            <i class="fas fa-trash"></i> Clear Logs
                        </button>
                    </div>

                    <div style="margin-top: 10px;">
                        <label style="display: inline-flex; align-items: center;">
                            <input type="checkbox" name="clear_pending" style="margin-right: 8px;">
                            Also clear pending payments when clearing logs
                        </label>
                    </div>
                </form>

                <?php if (!empty($log_content)): ?>
                    <div class="log-container">
                        <?php echo htmlspecialchars($log_content); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <a href="../cashier_dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <script>
        function openTab(evt, tabName) {
            var i, tabContent, tabButtons;

            // Hide all tab content
            tabContent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabContent.length; i++) {
                tabContent[i].className = tabContent[i].className.replace(" active", "");
            }

            // Remove active class from all tab buttons
            tabButtons = document.getElementsByClassName("tab-button");
            for (i = 0; i < tabButtons.length; i++) {
                tabButtons[i].className = tabButtons[i].className.replace(" active", "");
            }

            // Show the current tab and add active class to the button
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
