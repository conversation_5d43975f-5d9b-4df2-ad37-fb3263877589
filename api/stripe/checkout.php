<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once __DIR__ . '/../config.php';
require __DIR__ . "/vendor/autoload.php";

// Use Stripe API key from config
\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

$checkout_session = \Stripe\Checkout\Session::create([
    "mode" => "payment",
    "success_url" => "https://app.waghi.tech/stripe/success.php",
    "cancel_url" => "https://app.waghi.tech/stripe/index.php",
    "locale" => "auto",
    "line_items" => [
        [
            "quantity" => 1,
            "price_data" => [
                "currency" => "pgk",
                "unit_amount" => 20000,
                "product_data" => [
                    "name" => "Adobe Master Collection"
                ]
            ]
        ],


        /*

            [
            "quantity" => 2,
            "price_data" => [
                "currency" => "pgk",
                "unit_amount" => 5000,
                "product_data" => [
                    "name" => "MS Office + Windows 11 pro"
                ]
            ]
        ]

        */
    ]
]);

http_response_code(303);
header("Location: " . $checkout_session->url);