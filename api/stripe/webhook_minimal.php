<?php
// Minimal webhook for testing
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file
$log_file = $logs_dir . 'webhook_minimal.log';

// Log that we started
file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Minimal webhook started' . PHP_EOL, 
    FILE_APPEND);

// Just return success
http_response_code(200);
echo "Minimal webhook working";

file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Minimal webhook completed' . PHP_EOL, 
    FILE_APPEND);
?>
