<?php
// This is a simple test endpoint to verify that webhooks can reach your server

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file path
$log_file = $logs_dir . 'stripe_webhook_test.log';

// Log the request
file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Test webhook received' . PHP_EOL, 
    FILE_APPEND);

// Log request method
file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Request method: ' . $_SERVER['REQUEST_METHOD'] . PHP_EOL, 
    FILE_APPEND);

// Log all headers
$headers = getallheaders();
$headers_str = json_encode($headers, JSON_PRETTY_PRINT);
file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Headers: ' . $headers_str . PHP_EOL, 
    FILE_APPEND);

// Log raw payload
$payload = file_get_contents('php://input');
file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Payload: ' . $payload . PHP_EOL, 
    FILE_APPEND);

// Return a 200 OK response
http_response_code(200);
echo json_encode(['status' => 'success', 'message' => 'Test webhook received']);
?>
