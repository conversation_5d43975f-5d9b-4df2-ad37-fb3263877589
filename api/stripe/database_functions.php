<?php
// Database connection function for reuse
function getPdoConnection() {
    return new PDO('mysql:host=localhost;dbname=u787474055_backup', 'u787474055_backup', 'Blackpanther707@');
}

// Function to store payment information in the database
function storePaymentInDatabase($student_id, $amount, $currency, $payment_status) {
    $pdo = getPdoConnection();
    $stmt = $pdo->prepare("INSERT INTO payments (student_id, amount, currency, payment_status, payment_date) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([$student_id, $amount, $currency, $payment_status]);
}

// Function to fetch all payments for a specific student, ordered by payment date
function fetchPaymentsForStudent($student_id) {
    $pdo = getPdoConnection();
    $stmt = $pdo->prepare("SELECT * FROM payments WHERE student_id = ? ORDER BY payment_date DESC");
    $stmt->execute([$student_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Function to get the total paid amount by a student
function getTotalPaidAmount($student_id) {
    $pdo = getPdoConnection();
    $sql = "SELECT COALESCE(SUM(amount), 0) as total_paid FROM payments WHERE student_id = ? AND payment_status = 'completed'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['total_paid'];
}
