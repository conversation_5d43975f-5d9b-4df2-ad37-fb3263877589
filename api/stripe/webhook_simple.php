<?php
// This is a simplified webhook handler that doesn't require database connection

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file path
$log_file = $logs_dir . 'stripe_webhook_simple.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL, 
        FILE_APPEND);
}

// Start logging
log_message('Simple webhook handler started');

try {
    // Include Stripe library
    $vendor_autoload = __DIR__ . '/vendor/autoload.php';
    if (file_exists($vendor_autoload)) {
        require $vendor_autoload;
        log_message('Successfully included vendor/autoload.php');
    } else {
        throw new Exception('Could not find vendor/autoload.php');
    }
    
    // Set Stripe API key
    \Stripe\Stripe::setApiKey('sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');
    log_message('Set Stripe API key');
    
    // Get and log payload
    $payload = @file_get_contents('php://input');
    log_message('Received payload: ' . $payload);
    
    // Check for Stripe signature
    $sig_header = isset($_SERVER['HTTP_STRIPE_SIGNATURE']) ? $_SERVER['HTTP_STRIPE_SIGNATURE'] : '';
    log_message('Stripe Signature: ' . $sig_header);
    
    // Parse the payload without signature verification
    if (!empty($payload)) {
        $data = json_decode($payload);
        if (json_last_error() === JSON_ERROR_NONE) {
            log_message('Successfully parsed JSON payload');
            
            // Log event type if available
            if (isset($data->type)) {
                log_message('Event type: ' . $data->type);
                
                // Handle checkout.session.completed event
                if ($data->type === 'checkout.session.completed') {
                    log_message('Processing checkout.session.completed event');
                    
                    // Extract payment details
                    $session = $data->data->object;
                    
                    if (isset($session->client_reference_id)) {
                        $student_id = $session->client_reference_id;
                        log_message('Student ID: ' . $student_id);
                    } else {
                        log_message('WARNING: No client_reference_id found in session');
                    }
                    
                    if (isset($session->payment_intent)) {
                        $transaction_id = $session->payment_intent;
                        log_message('Transaction ID: ' . $transaction_id);
                    } else {
                        log_message('WARNING: No payment_intent found in session');
                    }
                    
                    if (isset($session->amount_total)) {
                        $amount = $session->amount_total / 100; // Convert to dollars
                        log_message('Amount: ' . $amount);
                    } else {
                        log_message('WARNING: No amount_total found in session');
                    }
                    
                    // Log that we would normally update the database here
                    log_message('In a normal webhook, we would now:');
                    log_message('1. Insert a record into the payments table');
                    log_message('2. Update the student\'s payment_status to "paid"');
                    
                    // Create a file record of the payment for manual processing
                    $payment_record = [
                        'student_id' => $student_id ?? 'unknown',
                        'transaction_id' => $transaction_id ?? 'unknown',
                        'amount' => $amount ?? 0,
                        'timestamp' => time(),
                        'date' => date('Y-m-d H:i:s')
                    ];
                    
                    // Save to a pending payments file
                    $pending_file = $logs_dir . 'pending_payments.json';
                    
                    // Read existing pending payments
                    $pending_payments = [];
                    if (file_exists($pending_file)) {
                        $pending_content = file_get_contents($pending_file);
                        if (!empty($pending_content)) {
                            $pending_payments = json_decode($pending_content, true) ?: [];
                        }
                    }
                    
                    // Add new payment
                    $pending_payments[] = $payment_record;
                    
                    // Save back to file
                    file_put_contents($pending_file, json_encode($pending_payments, JSON_PRETTY_PRINT));
                    log_message('Saved payment record to pending_payments.json for manual processing');
                } else {
                    log_message('Ignoring event of type: ' . $data->type);
                }
            } else {
                log_message('No event type in payload');
            }
        } else {
            log_message('ERROR: Failed to parse JSON payload: ' . json_last_error_msg());
        }
    } else {
        log_message('Empty payload received');
    }
} catch (Exception $e) {
    log_message('ERROR: ' . $e->getMessage());
}

// End logging
log_message('Simple webhook handler completed');

// Return a 200 response to acknowledge receipt
http_response_code(200);
echo json_encode(['status' => 'success', 'message' => 'Webhook received and logged']);
?>
