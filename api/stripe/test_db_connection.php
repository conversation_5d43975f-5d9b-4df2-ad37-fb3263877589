<?php
// Test database connection for webhook debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Connection Test</h1>";

try {
    require '../db_conn.php';
    echo "<p>✅ Database connection file loaded successfully</p>";
    
    if ($conn && !$conn->connect_error) {
        echo "<p>✅ Database connection established successfully</p>";
        echo "<p>Connected to database: " . DB_NAME . "</p>";
        echo "<p>Host: " . DB_HOST . "</p>";
        echo "<p>User: " . DB_USER . "</p>";
        
        // Test if tables exist
        $tables = ['students', 'payments'];
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<p>✅ Table '$table' exists</p>";
                
                // Show table structure
                $structure = $conn->query("DESCRIBE $table");
                if ($structure) {
                    echo "<details><summary>Table structure for '$table'</summary>";
                    echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                    while ($row = $structure->fetch_assoc()) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table></details>";
                }
            } else {
                echo "<p>❌ Table '$table' does not exist</p>";
            }
        }
        
        // Test inserting a test payment
        echo "<h2>Testing Payment Insert</h2>";
        try {
            $test_student_id = 'TEST_STUDENT_ID';
            $test_amount = 100.00;
            $test_transaction_id = 'test_transaction_' . time();
            
            $stmt = $conn->prepare("INSERT INTO payments (student_id, amount, payment_date, payment_method, transaction_id) VALUES (?, ?, NOW(), 'credit_card', ?)");
            if ($stmt) {
                $stmt->bind_param("sds", $test_student_id, $test_amount, $test_transaction_id);
                if ($stmt->execute()) {
                    echo "<p>✅ Test payment insert successful</p>";
                    
                    // Clean up test data
                    $delete_stmt = $conn->prepare("DELETE FROM payments WHERE transaction_id = ?");
                    $delete_stmt->bind_param("s", $test_transaction_id);
                    $delete_stmt->execute();
                    echo "<p>✅ Test payment cleaned up</p>";
                } else {
                    echo "<p>❌ Test payment insert failed: " . $stmt->error . "</p>";
                }
                $stmt->close();
            } else {
                echo "<p>❌ Failed to prepare payment insert statement: " . $conn->error . "</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Exception during payment test: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>❌ Database connection failed: " . ($conn ? $conn->connect_error : 'Connection object is null') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Configuration Check</h2>";
echo "<p>DB_HOST: " . (defined('DB_HOST') ? DB_HOST : 'Not defined') . "</p>";
echo "<p>DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'Not defined') . "</p>";
echo "<p>DB_USER: " . (defined('DB_USER') ? DB_USER : 'Not defined') . "</p>";
echo "<p>DB_PASSWORD: " . (defined('DB_PASSWORD') ? '[HIDDEN]' : 'Not defined') . "</p>";

echo "<h2>PHP Info</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQL Extension: " . (extension_loaded('mysqli') ? 'Loaded' : 'Not loaded') . "</p>";

?>
