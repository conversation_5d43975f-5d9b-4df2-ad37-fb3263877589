<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once __DIR__ . '/../config.php';
require '../db_conn.php';

// Check if user is admin or cashier
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'cashier')) {
    header("Location: ../login.php");
    exit();
}

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file path
$log_file = $logs_dir . 'pending_process.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL, 
        FILE_APPEND);
}

// Initialize variables
$message = '';
$error = '';
$pending_payments = [];
$processed_count = 0;
$failed_count = 0;

// Path to pending payments file
$pending_file = $logs_dir . 'pending_payments.json';

// Check if pending payments file exists
if (file_exists($pending_file)) {
    $pending_content = file_get_contents($pending_file);
    if (!empty($pending_content)) {
        $pending_payments = json_decode($pending_content, true) ?: [];
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['process'])) {
    if (empty($pending_payments)) {
        $message = "No pending payments to process.";
    } else {
        log_message("Starting to process " . count($pending_payments) . " pending payments");
        
        // Keep track of successfully processed payments
        $processed_payments = [];
        
        // Process each pending payment
        foreach ($pending_payments as $index => $payment) {
            $student_id = $payment['student_id'] ?? 'unknown';
            $transaction_id = $payment['transaction_id'] ?? 'unknown';
            $amount = $payment['amount'] ?? 0;
            
            log_message("Processing payment for student ID: $student_id, Amount: $amount, Transaction ID: $transaction_id");
            
            // Skip if student_id or transaction_id is unknown
            if ($student_id === 'unknown' || $transaction_id === 'unknown') {
                log_message("Skipping payment with unknown student_id or transaction_id");
                $failed_count++;
                continue;
            }
            
            try {
                // Start transaction
                $conn->begin_transaction();
                
                // Check if payment already exists
                $check_stmt = $conn->prepare("SELECT id FROM payments WHERE transaction_id = ?");
                $check_stmt->bind_param("s", $transaction_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    log_message("Payment already exists for transaction ID: $transaction_id");
                    
                    // Just update student status if needed
                    $update_stmt = $conn->prepare("UPDATE students SET payment_status = 'paid' WHERE student_id = ? AND payment_status != 'paid'");
                    $update_stmt->bind_param("s", $student_id);
                    $update_stmt->execute();
                    
                    if ($update_stmt->affected_rows > 0) {
                        log_message("Updated payment status for student ID: $student_id");
                        $processed_count++;
                        $processed_payments[] = $index;
                    } else {
                        log_message("Student ID: $student_id already has paid status");
                        $processed_payments[] = $index; // Still mark as processed
                    }
                    
                    $update_stmt->close();
                } else {
                    // Insert new payment
                    $insert_stmt = $conn->prepare("INSERT INTO payments (student_id, amount, payment_date, payment_method, transaction_id) VALUES (?, ?, FROM_UNIXTIME(?), 'credit_card', ?)");
                    $timestamp = $payment['timestamp'] ?? time();
                    $insert_stmt->bind_param("sdis", $student_id, $amount, $timestamp, $transaction_id);
                    
                    if ($insert_stmt->execute()) {
                        log_message("Inserted payment record for student ID: $student_id");
                        
                        // Update student status
                        $update_stmt = $conn->prepare("UPDATE students SET payment_status = 'paid' WHERE student_id = ?");
                        $update_stmt->bind_param("s", $student_id);
                        
                        if ($update_stmt->execute()) {
                            log_message("Updated payment status for student ID: $student_id");
                            $processed_count++;
                            $processed_payments[] = $index;
                        } else {
                            throw new Exception("Failed to update student status: " . $update_stmt->error);
                        }
                        
                        $update_stmt->close();
                    } else {
                        throw new Exception("Failed to insert payment record: " . $insert_stmt->error);
                    }
                    
                    $insert_stmt->close();
                }
                
                $check_stmt->close();
                $conn->commit();
                
            } catch (Exception $e) {
                $conn->rollback();
                log_message("Error processing payment: " . $e->getMessage());
                $failed_count++;
            }
        }
        
        // Remove processed payments from the pending list
        if (!empty($processed_payments)) {
            foreach (array_reverse($processed_payments) as $index) {
                array_splice($pending_payments, $index, 1);
            }
            
            // Save updated pending payments
            file_put_contents($pending_file, json_encode($pending_payments, JSON_PRETTY_PRINT));
            log_message("Updated pending payments file, removed " . count($processed_payments) . " processed payments");
        }
        
        $message = "Processed $processed_count payments successfully. Failed: $failed_count.";
        if (count($pending_payments) > 0) {
            $message .= " " . count($pending_payments) . " payments still pending.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Process Pending Payments</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        button {
            background-color: #ffbf00;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover {
            background-color: #e6ac00;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #666;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-sync"></i> Process Pending Payments</h1>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <p>
            This tool processes payments that were received by the webhook but couldn't be saved to the database.
            These payments are stored in a temporary file and can be processed manually.
        </p>
        
        <?php if (empty($pending_payments)): ?>
            <div class="alert alert-warning">
                No pending payments found.
            </div>
        <?php else: ?>
            <h2>Pending Payments (<?php echo count($pending_payments); ?>)</h2>
            
            <table>
                <thead>
                    <tr>
                        <th>Student ID</th>
                        <th>Amount</th>
                        <th>Transaction ID</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pending_payments as $payment): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($payment['student_id'] ?? 'unknown'); ?></td>
                            <td>K<?php echo number_format($payment['amount'] ?? 0, 2); ?></td>
                            <td><?php echo htmlspecialchars($payment['transaction_id'] ?? 'unknown'); ?></td>
                            <td><?php echo htmlspecialchars($payment['date'] ?? 'unknown'); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <form method="post" action="">
                <button type="submit" name="process" onclick="return confirm('Are you sure you want to process these payments?');">
                    <i class="fas fa-sync"></i> Process Pending Payments
                </button>
            </form>
        <?php endif; ?>
        
        <a href="../cashier_dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</body>
</html>
