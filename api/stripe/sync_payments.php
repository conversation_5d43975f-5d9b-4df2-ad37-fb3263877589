<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once __DIR__ . '/../config.php';
require 'vendor/autoload.php';
require '../db_conn.php';

// Check if user is admin or cashier
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'cashier')) {
    header("Location: ../login.php");
    exit();
}

// Set Stripe API key
\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file path
$log_file = $logs_dir . 'stripe_sync.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL,
        FILE_APPEND);

    // Also add to PHP error log
    error_log($message);
}

// Initialize variables
$message = '';
$error = '';
$updated_count = 0;
$sync_days = isset($_POST['days']) ? intval($_POST['days']) : 30; // Default to last 30 days

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['sync'])) {
    try {
        log_message("Starting payment sync for the last $sync_days days");

        // Calculate the date for filtering
        $date_limit = new DateTime();
        $date_limit->modify("-$sync_days days");
        $date_limit_str = $date_limit->format('Y-m-d\TH:i:s\Z');

        // Retrieve payments from Stripe
        $checkout_sessions = \Stripe\Checkout\Session::all([
            'limit' => 100,
            'created' => [
                'gte' => $date_limit->getTimestamp()
            ],
            'status' => 'complete'
        ]);

        log_message("Retrieved " . count($checkout_sessions->data) . " completed checkout sessions from Stripe");

        // Process each session
        foreach ($checkout_sessions->data as $session) {
            // Skip if no client_reference_id (student_id)
            if (empty($session->client_reference_id)) {
                log_message("Skipping session {$session->id} - No client_reference_id");
                continue;
            }

            $student_id = $session->client_reference_id;
            $transaction_id = $session->payment_intent;
            $amount = $session->amount_total / 100; // Convert to dollars

            log_message("Processing payment for student ID: $student_id, Amount: $amount, Transaction ID: $transaction_id");

            // Check if payment already exists in database
            // First, let's check what columns exist in the payments table
            $table_check = $conn->query("DESCRIBE payments");
            $columns = [];
            if ($table_check) {
                while ($row = $table_check->fetch_assoc()) {
                    $columns[] = $row['Field'];
                }
                log_message("Payments table columns: " . implode(', ', $columns));
            } else {
                log_message("Error checking payments table structure: " . $conn->error);
                continue;
            }

            // Use appropriate column for checking existing payments
            $primary_column = 'transaction_id'; // Default to transaction_id
            if (in_array('id', $columns)) {
                $check_query = "SELECT id FROM payments WHERE transaction_id = ?";
            } else if (in_array('payment_id', $columns)) {
                $check_query = "SELECT payment_id FROM payments WHERE transaction_id = ?";
            } else if (in_array('transaction_id', $columns)) {
                $check_query = "SELECT transaction_id FROM payments WHERE transaction_id = ?";
            } else {
                log_message("No suitable primary key found in payments table");
                continue;
            }

            $check_stmt = $conn->prepare($check_query);
            if (!$check_stmt) {
                log_message("Error preparing check statement: " . $conn->error);
                continue;
            }

            $check_stmt->bind_param("s", $transaction_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                log_message("Payment already exists in database for transaction ID: $transaction_id");

                // Check if student status needs updating
                $student_stmt = $conn->prepare("SELECT payment_status FROM students WHERE student_id = ?");
                $student_stmt->bind_param("s", $student_id);
                $student_stmt->execute();
                $student_result = $student_stmt->get_result();

                if ($student_result->num_rows > 0) {
                    $student_data = $student_result->fetch_assoc();

                    if ($student_data['payment_status'] !== 'paid') {
                        // Update student payment status
                        $update_stmt = $conn->prepare("UPDATE students SET payment_status = 'paid' WHERE student_id = ?");
                        $update_stmt->bind_param("s", $student_id);

                        if ($update_stmt->execute()) {
                            $updated_count++;
                            log_message("Updated payment status for student ID: $student_id");
                        } else {
                            log_message("Error updating student status: " . $update_stmt->error);
                        }

                        $update_stmt->close();
                    } else {
                        log_message("Student ID: $student_id already has paid status");
                    }
                } else {
                    log_message("Student ID: $student_id not found in database");
                }

                $student_stmt->close();
            } else {
                // Insert new payment record
                $conn->begin_transaction();

                try {
                    // Build INSERT query based on available columns
                    $insert_columns = [];
                    $insert_values = [];
                    $insert_params = "";
                    $bind_values = [];

                    // Always include these if they exist
                    if (in_array('student_id', $columns)) {
                        $insert_columns[] = 'student_id';
                        $insert_values[] = '?';
                        $insert_params .= 's';
                        $bind_values[] = $student_id;
                    }

                    if (in_array('amount', $columns)) {
                        $insert_columns[] = 'amount';
                        $insert_values[] = '?';
                        $insert_params .= 'd';
                        $bind_values[] = $amount;
                    }

                    if (in_array('payment_date', $columns)) {
                        $insert_columns[] = 'payment_date';
                        $insert_values[] = 'FROM_UNIXTIME(?)';
                        $insert_params .= 'i';
                        $bind_values[] = $session->created;
                    } else if (in_array('date', $columns)) {
                        $insert_columns[] = 'date';
                        $insert_values[] = 'FROM_UNIXTIME(?)';
                        $insert_params .= 'i';
                        $bind_values[] = $session->created;
                    }

                    if (in_array('payment_method', $columns)) {
                        $insert_columns[] = 'payment_method';
                        $insert_values[] = '?';
                        $insert_params .= 's';
                        $bind_values[] = 'credit_card';
                    } else if (in_array('method', $columns)) {
                        $insert_columns[] = 'method';
                        $insert_values[] = '?';
                        $insert_params .= 's';
                        $bind_values[] = 'credit_card';
                    }

                    if (in_array('transaction_id', $columns)) {
                        $insert_columns[] = 'transaction_id';
                        $insert_values[] = '?';
                        $insert_params .= 's';
                        $bind_values[] = $transaction_id;
                    }

                    if (empty($insert_columns)) {
                        throw new Exception("No compatible columns found in payments table");
                    }

                    $insert_query = "INSERT INTO payments (" . implode(', ', $insert_columns) . ") VALUES (" . implode(', ', $insert_values) . ")";
                    log_message("Insert query: " . $insert_query);

                    $insert_stmt = $conn->prepare($insert_query);
                    if (!$insert_stmt) {
                        throw new Exception("Error preparing insert statement: " . $conn->error);
                    }

                    // Bind parameters dynamically
                    if (!empty($bind_values)) {
                        $insert_stmt->bind_param($insert_params, ...$bind_values);
                    }

                    if (!$insert_stmt->execute()) {
                        throw new Exception("Error inserting payment: " . $insert_stmt->error);
                    }

                    log_message("Successfully inserted payment record");

                    // Update student status
                    $update_stmt = $conn->prepare("UPDATE students SET payment_status = 'paid' WHERE student_id = ?");
                    $update_stmt->bind_param("s", $student_id);

                    if (!$update_stmt->execute()) {
                        throw new Exception("Error updating student status: " . $update_stmt->error);
                    }

                    $conn->commit();
                    $updated_count++;
                    log_message("Added new payment and updated status for student ID: $student_id");

                    $insert_stmt->close();
                    $update_stmt->close();
                } catch (Exception $e) {
                    $conn->rollback();
                    log_message("Transaction failed: " . $e->getMessage());
                }
            }

            $check_stmt->close();
        }

        $message = "Sync completed successfully. Updated $updated_count student records.";
        log_message($message);

    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        log_message("Error during sync: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sync Stripe Payments</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        form {
            margin-top: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #ffbf00;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover {
            background-color: #e6ac00;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #666;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-sync"></i> Sync Stripe Payments</h1>

        <?php if (!empty($message)): ?>
            <div class="alert alert-success">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <p>
            Use this tool to synchronize payments from Stripe with your database.
            This will update student payment statuses for payments that have been processed in Stripe but not updated in your database.
        </p>

        <form method="post" action="">
            <label for="days">Sync payments from the last:</label>
            <input type="number" id="days" name="days" value="<?php echo $sync_days; ?>" min="1" max="365">
            <p><small>Number of days to look back for payments (default: 30)</small></p>

            <button type="submit" name="sync">
                <i class="fas fa-sync"></i> Sync Payments
            </button>
        </form>

        <a href="../cashier_dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</body>
</html>
