<?php

require __DIR__ . "/../vendor/autoload.php";

$stripe_secret_key = "sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH";

\Stripe\Stripe::setApiKey($stripe_secret_key);

session_start();

$student_id = $_SESSION['username']; // Assuming the username is the student ID

$checkout_session = \Stripe\Checkout\Session::create([
    "mode" => "payment",
    "success_url" => "https://dbtionline.waghitech.com/stripe/success.php",
    "cancel_url" => "https://dbtionline.waghitech.com/student_dashboard.php",
    "locale" => "auto",
    "client_reference_id" => $student_id,
    "line_items" => [
        [
            "quantity" => 1,
            "price_data" => [
                "currency" => "pgk",
                "unit_amount" => 460000,
                "product_data" => [
                    "name" => "First Semester Payment"
                ]
            ]
        ]
    ]
]);

// Instead of redirecting, return the session ID
echo json_encode(['sessionId' => $checkout_session->id]);
?>

<script src="https://js.stripe.com/v3/"></script>
<script>
var stripe = Stripe('pk_test_51OpK5NKnyip3i5w1gad6XcasjQlbwMsov86qhLdkmrL8vuKmqVxCzjXFtFbH9vjIDa75flwKllwmU37FsKItXkgB008kcsU8wY'); // Replace with your actual publishable key
var sessionId = '<?php echo $checkout_session->id; ?>';

stripe.redirectToCheckout({ sessionId: sessionId }).then(function(result) {
    if (result.error) {
        // Handle error
    } else {
        // Check session status
        checkSessionStatus(sessionId);
    }
});

function checkSessionStatus(sessionId) {
    fetch('/check_session.php?session_id=' + sessionId)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'paid') {
            // Update UI or redirect to success page
            window.location.href = '/payment_success.php';
        } else {
            // Handle other statuses
        }
    });
}
</script>
