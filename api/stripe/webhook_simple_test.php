<?php
// Simple webhook test to isolate issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file
$log_file = $logs_dir . 'webhook_simple_test.log';

// Log that the script started
file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Simple webhook test started' . PHP_EOL, 
    FILE_APPEND);

// Test 1: Basic PHP functionality
try {
    $test_data = ['test' => 'value'];
    $json_test = json_encode($test_data);
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ✅ Basic PHP functionality works' . PHP_EOL, 
        FILE_APPEND);
} catch (Exception $e) {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ❌ Basic PHP functionality failed: ' . $e->getMessage() . PHP_EOL, 
        FILE_APPEND);
}

// Test 2: Check if vendor/autoload.php exists
if (file_exists('vendor/autoload.php')) {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ✅ vendor/autoload.php exists' . PHP_EOL, 
        FILE_APPEND);
    
    try {
        require 'vendor/autoload.php';
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ✅ vendor/autoload.php loaded successfully' . PHP_EOL, 
            FILE_APPEND);
    } catch (Exception $e) {
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ❌ vendor/autoload.php failed to load: ' . $e->getMessage() . PHP_EOL, 
            FILE_APPEND);
    }
} else {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ❌ vendor/autoload.php does not exist' . PHP_EOL, 
        FILE_APPEND);
}

// Test 3: Check if config.php exists
if (file_exists('../config.php')) {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ✅ ../config.php exists' . PHP_EOL, 
        FILE_APPEND);
    
    try {
        require '../config.php';
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ✅ ../config.php loaded successfully' . PHP_EOL, 
            FILE_APPEND);
    } catch (Exception $e) {
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ❌ ../config.php failed to load: ' . $e->getMessage() . PHP_EOL, 
            FILE_APPEND);
    }
} else {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ❌ ../config.php does not exist' . PHP_EOL, 
        FILE_APPEND);
}

// Test 4: Check if db_conn.php exists
if (file_exists('../db_conn.php')) {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ✅ ../db_conn.php exists' . PHP_EOL, 
        FILE_APPEND);
    
    try {
        require '../db_conn.php';
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ✅ ../db_conn.php loaded successfully' . PHP_EOL, 
            FILE_APPEND);
        
        if (isset($conn) && $conn && !$conn->connect_error) {
            file_put_contents($log_file, 
                date('Y-m-d H:i:s') . ' - ✅ Database connection successful' . PHP_EOL, 
                FILE_APPEND);
        } else {
            file_put_contents($log_file, 
                date('Y-m-d H:i:s') . ' - ❌ Database connection failed: ' . (isset($conn) && $conn ? $conn->connect_error : 'Connection object not created') . PHP_EOL, 
                FILE_APPEND);
        }
    } catch (Exception $e) {
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ❌ ../db_conn.php failed to load: ' . $e->getMessage() . PHP_EOL, 
            FILE_APPEND);
    }
} else {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ❌ ../db_conn.php does not exist' . PHP_EOL, 
        FILE_APPEND);
}

// Test 5: Check Stripe class availability
try {
    if (class_exists('\Stripe\Stripe')) {
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ✅ Stripe class is available' . PHP_EOL, 
            FILE_APPEND);
        
        \Stripe\Stripe::setApiKey('sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ✅ Stripe API key set successfully' . PHP_EOL, 
            FILE_APPEND);
    } else {
        file_put_contents($log_file, 
            date('Y-m-d H:i:s') . ' - ❌ Stripe class is not available' . PHP_EOL, 
            FILE_APPEND);
    }
} catch (Exception $e) {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ❌ Stripe test failed: ' . $e->getMessage() . PHP_EOL, 
        FILE_APPEND);
}

// Test 6: Process a simple webhook payload
try {
    $payload = file_get_contents('php://input');
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - Received payload: ' . $payload . PHP_EOL, 
        FILE_APPEND);
    
    if (!empty($payload)) {
        $data = json_decode($payload, true);
        if ($data) {
            file_put_contents($log_file, 
                date('Y-m-d H:i:s') . ' - ✅ Payload decoded successfully' . PHP_EOL, 
                FILE_APPEND);
        } else {
            file_put_contents($log_file, 
                date('Y-m-d H:i:s') . ' - ❌ Failed to decode payload' . PHP_EOL, 
                FILE_APPEND);
        }
    }
} catch (Exception $e) {
    file_put_contents($log_file, 
        date('Y-m-d H:i:s') . ' - ❌ Payload processing failed: ' . $e->getMessage() . PHP_EOL, 
        FILE_APPEND);
}

file_put_contents($log_file, 
    date('Y-m-d H:i:s') . ' - Simple webhook test completed' . PHP_EOL . PHP_EOL, 
    FILE_APPEND);

// Return success
http_response_code(200);
echo "Simple webhook test completed. Check logs for details.";
?>
