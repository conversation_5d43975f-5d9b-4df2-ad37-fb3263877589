
<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once __DIR__ . '/../config.php';
require 'vendor/stripe/stripe-php/init.php';
require '../db_conn.php';

// Use Stripe API key from config
\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

$session_id = $_GET['session_id'];
$session = \Stripe\Checkout\Session::retrieve($session_id);

if ($session->payment_status === 'paid') {
    $user_id = $session->client_reference_id;
    $amount = $session->amount_total / 100;

    // Update database
    $sql = "UPDATE payments SET payment_status = 'paid', amount = ?, payment_date = NOW() WHERE user_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$amount, $user_id]);

    echo json_encode(['status' => 'paid']);
} else {
    echo json_encode(['status' => $session->payment_status]);
}
