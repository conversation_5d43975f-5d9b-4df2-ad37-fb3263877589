<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cashier Dashboard - Fetched Transactions in PGK</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table-container {
            max-height: 800px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
            position: sticky;
            top: 0;
        }
        .send-receipt-btn {
            padding: 5px 10px;
            background-color: #28a745;
            color: #fff;
            border: none;
            cursor: pointer;
        }
        .send-receipt-btn:hover {
            background-color: #218838;
        }

        /* Add search bar styling */
        #searchInput {
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        #searchInput:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <h1>Cashier Dashboard - Fetched Transactions in PGK</h1>
    <div style="margin: 20px 0;">
        <input type="text" id="searchInput" placeholder="Search by ID, Email or Name..." style="padding: 8px; width: 300px;">
    </div>

    <?php
    session_start();
    require 'transection_init.php';

    // Verify user is logged in and has cashier role
    if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'cashier') {
        header("Location: ../login.php");
        exit();
    }

    // Rest of your existing transaction page code continues here...
    date_default_timezone_set('Pacific/Port_Moresby');

    // Fetch the transactions from Stripe
    try {
        $limit = 20; // Show 20 transactions
        $starting_after = isset($_GET['starting_after']) ? $_GET['starting_after'] : null;
        $params = ['limit' => $limit];
        if ($starting_after) {
            $params['starting_after'] = $starting_after;
        }

        // Fetch PaymentIntents from Stripe
        $transactions = \Stripe\PaymentIntent::all($params);

        if (empty($transactions->data)) {
            echo '<table><tr><td colspan="8">No transactions found.</td></tr></table>';
        } else {
            echo '<div class="table-container">
                    <table>
                    <thead>
                        <tr>
                            <th>Transaction ID</th>
                            <th>Student Email</th>
                            <th>Student Name</th>
                            <th>Amount (PGK)</th>
                            <th>Payment Method</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Send Receipt</th>
                        </tr>
                    </thead>
                    <tbody>';

            // Loop through each transaction and display in table
            foreach ($transactions->data as $transaction) {
                // Initialize guest email and name
                $guest_email = 'N/A';
                $guest_name = 'N/A';

                // Fetch details from the charges object
                if (!empty($transaction->charges->data)) {
                    $charge = $transaction->charges->data[0];
                    $guest_email = $charge->billing_details->email ?? $transaction->receipt_email ?? 'N/A';
                    $guest_name = $charge->billing_details->name ?? 'N/A';
                }

                // Fallback: Try to get details from payment method if charges are missing
                if ($guest_email === 'N/A' || $guest_name === 'N/A') {
                    if (!empty($transaction->payment_method)) {
                        try {
                            $payment_method = \Stripe\PaymentMethod::retrieve($transaction->payment_method);
                            $guest_email = $payment_method->billing_details->email ?? $guest_email;
                            $guest_name = $payment_method->billing_details->name ?? $guest_name;
                        } catch (Exception $e) {
                            error_log('Error retrieving payment method: ' . $e->getMessage());
                        }
                    }
                }

                // Retrieve payment method details if available
                $payment_method_info = 'N/A';
                if (!empty($transaction->payment_method)) {
                    try {
                        $payment_method = \Stripe\PaymentMethod::retrieve($transaction->payment_method);
                        $payment_method_info = ucfirst($payment_method->card->brand) . ' ending in ' . $payment_method->card->last4;
                    } catch (Exception $e) {
                        $payment_method_info = 'Error retrieving payment method';
                    }
                }

                // Show the original amount (Stripe amount is in cents, divide by 100)
                $converted_amount = number_format($transaction->amount / 100, 2);

                // Output table row with guest details
                echo '<tr>';
                echo '<td>' . $transaction->id . '</td>';
                echo '<td>' . htmlspecialchars($guest_email) . '</td>';
                echo '<td>' . htmlspecialchars($guest_name) . '</td>';
                echo '<td>PGK ' . $converted_amount . '</td>';
                echo '<td>' . $payment_method_info . '</td>';
                echo '<td>' . ucfirst($transaction->status) . '</td>';
                echo '<td>' . date('d/m/Y h:i A', $transaction->created) . '</td>'; // Format date for PNG
                echo '<td>
                    <form action="send_receipt.php" method="POST">
                        <input type="hidden" name="payment_intent_id" value="' . $transaction->id . '">
                        <input type="hidden" name="guest_email" value="' . htmlspecialchars($guest_email) . '">
                        <input type="hidden" name="guest_name" value="' . htmlspecialchars($guest_name) . '">
                        <input type="hidden" name="amount" value="' . $converted_amount . '">
                        <input type="hidden" name="payment_method_info" value="' . $payment_method_info . '">
                        <button type="submit" class="send-receipt-btn">Send Receipt</button>
                    </form>
                  </td>';
                echo '</tr>';
            }

            echo '</tbody></table></div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">Error fetching transactions: ' . $e->getMessage() . '</div>';
    }
    ?>
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function() {
            let searchText = this.value.toLowerCase();
            let tableRows = document.querySelectorAll('tbody tr');
            
            tableRows.forEach(row => {
                let transactionId = row.cells[0].textContent.toLowerCase();
                let email = row.cells[1].textContent.toLowerCase();
                let name = row.cells[2].textContent.toLowerCase();
                
                if (transactionId.includes(searchText) || 
                    email.includes(searchText) || 
                    name.includes(searchText)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
