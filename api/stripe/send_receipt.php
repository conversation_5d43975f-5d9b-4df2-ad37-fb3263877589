<?php
require '../PHPMailer-6.9.2/src/PHPMailer.php';
require '../PHPMailer-6.9.2/src/SMTP.php';
require '../PHPMailer-6.9.2/src/Exception.php';
require '../fpdf186/fpdf.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['payment_intent_id'])) {
    $transaction_id = $_POST['payment_intent_id'];
    $guest_email = $_POST['guest_email'];
    $guest_name = $_POST['guest_name'];
    $amount = $_POST['amount'];
    $payment_date = $_POST['payment_date'];

    // Fetch the payment amount from Stripe transaction
    require_once 'transection_init.php';

    try {
        $payment_intent = \Stripe\PaymentIntent::retrieve($transaction_id);
        $amount = $payment_intent->amount / 100; // Convert from cents to dollars
    } catch(Exception $e) {
        error_log('Error fetching payment amount: ' . $e->getMessage());
    }

    // Create PDF with correct amount
    $pdf = new FPDF();
    $pdf->AddPage();
    $pdf->SetFont('Arial', 'B', 17);
    $pdf->Cell(40, 10, 'Receipt for Payment: DBTI Online Registration ');
    $pdf->Ln(20);
    $pdf->SetFont('Arial', '', 14);
    $pdf->Cell(40, 10, 'Transaction ID: ' . $transaction_id);
    $pdf->Ln(10);
    $pdf->Cell(40, 10, 'Student Name: ' . $guest_name);
    $pdf->Ln(10);
    $pdf->Cell(40, 10, 'Amount: PGK ' . number_format($amount, 2));
    $pdf->Ln(10);
    // Format the date properly
    $formatted_date = date('d/m/Y h:i A', strtotime($payment_date));
    $pdf->Cell(40, 10, 'Payment Date: ' . $formatted_date);
    
    // Output the PDF to a file
    $pdf_file = 'DBTI_FEE_receipt.pdf';
    $pdf->Output('F', $pdf_file);

    // Send the email with the attached PDF receipt
    $mail = new PHPMailer();
    try {
        // Server settings
        $mail->isSMTP();  
        $mail->Host       = 'smtp.hostinger.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'Blackpanther707@707';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port       = 465;

        // Recipients
        $mail->setFrom('<EMAIL>', 'DBTI ONLINE REGISTRATION FEE RECEIPT');
        $mail->addAddress($guest_email, $guest_name);

        // Attach the PDF receipt
        $mail->addAttachment($pdf_file, 'Payment Receipt.pdf');

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Payment Receipt - Transaction ' . $transaction_id;
        $mail->Body    = 'Dear ' . $guest_name . ',<br><br>Thank you for your payment. Please find attached the receipt for your payment.<br><br>Regards,<br>Cashier, DON BOSCO TECHNOLOGICAL INSTITUTE ';

        $mail->send();
        echo 'Receipt sent successfully to ' . htmlspecialchars($guest_email);
    } catch (Exception $e) {
        echo 'Message could not be sent. Mailer Error: ' . $mail->ErrorInfo;
    }
}
?>
