<?php
session_start();
if (!isset($_SESSION['role']) || $_SESSION['role'] != 'cashier') {
    header("Location: login.php");
    exit();
}
require_once '../config/db_conn.php';

// Fetch recent payments
$sql = "SELECT p.student_id, p.amount, p.payment_date, u.username 
        FROM payments p 
        JOIN users u ON p.student_id = u.id 
        ORDER BY p.payment_date DESC 
        LIMIT 10";
$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Cashier Dashboard - DBTI Online Registration">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Cashier Dashboard - DBTI Online Registration</title>
</head>
<body>
    <nav>
        <div id="yt_logo" class="col-md-3 col-sm-12">
            <a class="yt_logo" href="#" title="Don Bosco Technological Institute">
                <img src="img/logo.webp" alt="DBTI Logo">
            </a>
        </div>
        <div class="heading">DBTI Online Registration</div>
        <span class="sideMenuButton" onclick="openNavbar()"> ☰ </span>
        <div class="navbar">
            <ul>
                <li><a href="../public/index.php">Home</a></li>
                <li><a href="#">About</a></li>
                <li><a href="cashier_dashboard.php">Dashboard</a></li>
                <li><a href="logout.php">Logout </a> <i class="fa-solid fa-user"></i></li>
            </ul>
        </div>
    </nav>
    <!-- Side navigation bar for responsive website -->
    <div class="sideNavigationBar" id="sideNavigationBar">
        <a href="#" class="closeButton" onclick="closeNavbar()"> ❌ </a>
        <a href="../public/index.php">Home</a>
        <a href="#">About</a>
        <a href="cashier_dashboard.php">Dashboard</a>
        <a href="logout.php">Logout</a>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="cashier_dashboard.php">Dashboard</a>
            <a href="javascript:void(0)" onclick="showPayments()">Recent Payments</a>
            <a href="javascript:void(0)" onclick="showStats()">Payment Stats</a>
        </div>
        <div class="main-content">
            <h2>Cashier Dashboard</h2>
            <div id="recentPayments">
                <h3>Recent Payments</h3>
                <table>
                    <tr>
                        <th>Student ID</th>
                        <th>Amount</th>
                        <th>Date</th>
                        <th>Status</th>
                    </tr>
                    <?php
                    if ($result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($row["student_id"]) . "</td>";
                            echo "<td>" . htmlspecialchars($row["amount"]) . " PGK</td>";
                            echo "<td>" . htmlspecialchars($row["payment_date"]) . "</td>";
                            echo "<td>Completed</td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='4'>No recent payments found</td></tr>";
                    }
                    ?>
                </table>
            </div>
            <div id="paymentStats" style="display:none;">
                <h3>Payment Statistics</h3>
                <div class="stat-box">
                    <h4>Total Payments Today</h4>
                    <p id="totalPaymentsToday"><?php echo $totalPaymentsToday; ?></p>
                </div>
                <div class="stat-box">
                    <h4>Total Amount Collected</h4>
                    <p id="totalAmountCollected"><?php echo $totalAmountCollected; ?> PGK</p>
                </div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>
<?php
$conn->close();
?>