<?php
require 'vendor/autoload.php';
require '../db_conn.php';
require_once '../config.php';

// Set Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');

// Set the Stripe webhook secret
$endpoint_secret = STRIPE_WEBHOOK_SECRET;

// Retrieve the raw payload and Stripe signature header
$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];

$event = null;

try {
    // Verify the webhook signature and construct the event
    $event = \Stripe\Webhook::constructEvent(
        $payload, $sig_header, $endpoint_secret
    );
} catch (\UnexpectedValueException $e) {
    // Invalid payload
    http_response_code(400);
    error_log("Invalid payload: " . $e->getMessage());
    exit();
} catch (\Stripe\Exception\SignatureVerificationException $e) {
    // Invalid signature
    http_response_code(400);
    error_log("Invalid signature: " . $e->getMessage());
    exit();
}

// Handle the 'checkout.session.completed' event
if ($event->type == 'checkout.session.completed') {
    $session = $event->data->object;
    
    // Retrieve payment details from the session
    $student_id = $session->client_reference_id; // Reference ID passed from the client
    $amount = $session->amount_total / 100; // Stripe amounts are in cents
    $transaction_id = $session->payment_intent; // Payment intent from Stripe
    
    // Start a transaction
    $conn->begin_transaction();
    
    try {
        // Insert payment record into the payments table
        $sql = "INSERT INTO payments (
            student_id, 
            amount, 
            payment_date, 
            payment_method, 
            transaction_id
        ) VALUES (?, ?, NOW(), 'credit_card', ?)";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            throw new Exception("Failed to prepare the SQL statement: " . $conn->error);
        }

        $stmt->bind_param("sds", $student_id, $amount, $transaction_id);
        if (!$stmt->execute()) {
            throw new Exception("Failed to execute payment insert: " . $stmt->error);
        }

        // Update student's payment status to 'paid'
        $update_sql = "UPDATE students SET payment_status = 'paid' WHERE student_id = ?";
        $update_stmt = $conn->prepare($update_sql);
        if ($update_stmt === false) {
            throw new Exception("Failed to prepare the update statement: " . $conn->error);
        }

        $update_stmt->bind_param("s", $student_id);
        if (!$update_stmt->execute()) {
            throw new Exception("Failed to execute student update: " . $update_stmt->error);
        }

        // Commit the transaction after successful operations
        $conn->commit();
        error_log("Payment recorded successfully for student ID: $student_id");
        
    } catch (Exception $e) {
        // Rollback the transaction on failure
        $conn->rollback();
        error_log("Error recording payment: " . $e->getMessage());
    } finally {
        // Ensure statements are closed
        if ($stmt) $stmt->close();
        if ($update_stmt) $update_stmt->close();
    }
}

// Respond with HTTP 200 to acknowledge receipt of the webhook
http_response_code(200);
?>
