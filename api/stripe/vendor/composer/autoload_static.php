<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita4ef5ecf716cc34a3517dc73428b528a
{
    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Stripe\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Stripe\\' => 
        array (
            0 => __DIR__ . '/..' . '/stripe/stripe-php/lib',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita4ef5ecf716cc34a3517dc73428b528a::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita4ef5ecf716cc34a3517dc73428b528a::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita4ef5ecf716cc34a3517dc73428b528a::$classMap;

        }, null, ClassLoader::class);
    }
}
