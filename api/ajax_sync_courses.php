<?php
// Define secure access constant for config.php
define('SECURE_ACCESS', true);

session_start();
require_once '../config/db_conn.php';
include 'course_data.php';

// Check if user is authorized
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

try {
    // Begin transaction
    $conn->begin_transaction();

    // Clear existing courses
    $conn->query("DELETE FROM courses");

    $total_programs = count($courses);
    $total_technologies = 0;
    $total_years = 0;
    $total_courses = 0;

    // Insert all courses
    foreach ($courses as $program => $technologies) {
        $total_technologies += count($technologies);

        foreach ($technologies as $tech => $years) {
            $total_years += count($years);

            foreach ($years as $year => $course_list) {
                foreach ($course_list as $course_id => $course_name) {
                    $semester = (substr($course_id, -1) == '1') ? 'Semester 1' : 'Semester 2';

                    $stmt = $conn->prepare("INSERT INTO courses (course_id, course_name, program, technology, year_level, semester) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->bind_param("ssssss", $course_id, $course_name, $program, $tech, $year, $semester);
                    $stmt->execute();

                    $total_courses++;
                }
            }
        }
    }

    // Commit transaction
    $conn->commit();

    // Return success and counts
    echo json_encode([
        'success' => true,
        'programs' => $total_programs,
        'technologies' => $total_technologies,
        'years' => $total_years,
        'courses' => $total_courses
    ]);
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>