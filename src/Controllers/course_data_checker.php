<?php
// <PERSON><PERSON><PERSON> to check course_data.php contents
include 'course_data.php';

echo "<h2>Course Data File Analysis</h2>";

// Check available programs
echo "<h3>Available Programs:</h3>";
echo "<ul>";
foreach ($courses as $program => $technologies) {
    echo "<li>$program";
    
    // Display technologies for each program
    echo "<ul>";
    foreach ($technologies as $tech => $years) {
        echo "<li>$tech";
        
        // Display years for each technology
        echo "<ul>";
        foreach ($years as $year => $course_list) {
            $course_count = count($course_list);
            echo "<li>$year - $course_count courses</li>";
        }
        echo "</ul>";
        
        echo "</li>";
    }
    echo "</ul>";
    
    echo "</li>";
}
echo "</ul>";

// Count total programs, technologies, and courses
$program_count = count($courses);
$tech_count = 0;
$course_count = 0;
$year_count = 0;

foreach ($courses as $program => $technologies) {
    $tech_count += count($technologies);
    
    foreach ($technologies as $tech => $years) {
        $year_count += count($years);
        
        foreach ($years as $year => $course_list) {
            $course_count += count($course_list);
        }
    }
}

echo "<h3>Summary:</h3>";
echo "<p>Total Programs: $program_count</p>";
echo "<p>Total Technologies: $tech_count</p>";
echo "<p>Total Year Levels: $year_count</p>";
echo "<p>Total Courses: $course_count</p>";

// Links to other tools
echo "<p><a href='sync_courses.php'>Sync Courses to Database</a> | <a href='check_courses.php'>Check Database</a></p>";
?> 