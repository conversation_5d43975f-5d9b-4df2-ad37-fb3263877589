<?php
session_start();
require_once '../../config/db_conn.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get student_id from session instead of form
    $student_id = $_SESSION['username']; 
    
    $first_name = $_POST['first_name'];
    $last_name = $_POST['last_name'];
    $gender = $_POST['gender'];
    $phone_number = $_POST['phone_number'];
    $residential_address = $_POST['residential_address'];
    $home_province = $_POST['home_province'];
    $guardian_name = $_POST['guardian_name'];
    $guardian_occupation = $_POST['guardian_occupation'];
    $guardian_phone_number = $_POST['guardian_phone_number'];
    $guardian_email = $_POST['guardian_email'];
    $student_email = $_POST['student_email'];
    $dob = $_POST['dob'];
    $program = $_POST['program'];
    $year_level = $_POST['year_level']; // New input

    // Update existing student record instead of insert
    $tech = $_POST['tech'];
        $update_sql = "UPDATE students SET 
                   first_name=?, last_name=?, gender=?, phone_number=?, 
                   residential_address=?, home_province=?, guardian_name=?, 
                   guardian_occupation=?, guardian_phone_number=?, guardian_email=?, 
                   student_email=?, dob=?, program=?, year_level=?, tech=?, 
                   registration_status='registered',
                   registration_timestamp=CURRENT_TIMESTAMP
                   WHERE student_id=?";
    
    $stmt = $conn->prepare($update_sql);
    $stmt->bind_param("ssssssssssssssss", 
        $first_name, $last_name, $gender, $phone_number,
        $residential_address, $home_province, $guardian_name,
        $guardian_occupation, $guardian_phone_number, $guardian_email,
        $student_email, $dob, $program, $year_level, $tech, $student_id
    );

    if ($stmt->execute()) {
        $_SESSION['message'] = "Registration successful!";
        header("Location: student_dashboard.php");
    } else {
        $_SESSION['error'] = "Error: Could not complete registration.";
        header("Location: registration.php");
    }

    $stmt->close();
    $conn->close();
}
?>






