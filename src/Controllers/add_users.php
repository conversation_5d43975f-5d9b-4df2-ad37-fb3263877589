<?php
session_start();
if ($_SESSION['role'] != 'admin') {
    header("Location: ../../public/login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="">
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>DBTI Online Registration</title>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

nav .heading {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

nav ul li a {
    font-size: 16px;
    color: #fff;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover {
    background-color: green;
    border-radius: 5px;
}

/* Sidebar Styles */
.sidebar {
    width: 250px; /* Sidebar width */
    background-color: #ffffff; /* White background */
    padding: 20px; /* Internal padding */
    height: calc(100vh - 80px); /* Full height minus navbar height */
    position: fixed; /* Keep it fixed on the side */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Shadow for depth */
    transition: background-color 0.3s; /* Smooth background color transition */
    overflow-y: auto; /* Allow scrolling if content exceeds height */
    display: block; /* Ensure it's displayed */
}

/* Sidebar Links */
.sidebar a {
    display: block;
    padding: 12px; /* Padding for better touch target */
    color: #333; /* Dark text color for visibility */
    text-decoration: none; /* Remove underline */
    margin-bottom: 15px; /* Spacing between links */
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s, color 0.3s; /* Smooth transition effects */
}

/* Sidebar Links Hover Effect */
.sidebar a:hover {
    background-color: orange; /* Background color change on hover */
    color: #fff; /* Change text color on hover for better contrast */
}

.heading {
    text-align: center;
}

form {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #ddd;
    width: 100%;
    max-width: 400px;
}

form h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.8rem;
}

form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

form input,
form select {
    width: 100%;
    padding: 0.8rem;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1rem;
    color: #333;
    transition: border-color 0.3s ease;
}

form input:focus,
form select:focus {
    border-color: #4a90e2;
    outline: none;
}

button[type="submit"] {
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
    font-weight: bold;
    color: #fff;
    background: #4a90e2;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button[type="submit"]:hover {
    background: #007bff;
}

a.btn {
    display: block;
    width: 100%;
    text-align: center;
    padding: 0.8rem;
    margin-top: 1rem;
    font-size: 1rem;
    font-weight: bold;
    color: #4a90e2;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-decoration: none;
    transition: background 0.3s ease, color 0.3s ease;
}

a.btn:hover {
    color: #007bff;
    background: #eee;
}

        /* Footer */
footer {
    background-color: #2d3748;
    color: #ecf0f1;
    padding: 40px 5%;
    text-align: center;
    position: fixed; /* Ensure footer is relative for fixed positioning */
    bottom: 0; /* Stick to bottom */
    width: 100%; /* Full width */
}

footer a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
}

footer a:hover {
    text-decoration: underline;
}
    </style>
    <nav>
        <div id="yt_logo" class="col-md-3 col-sm-12">
            <a class="yt_logo" href="../../public/index.php" title="Don Bosco Technological Institute">
                <img src="img/logo.webp" style="width: 120px;height: 100px;">
            </a>
        </div>
        <div class="heading">DBTI Online Registration</div>
        <div class="navbar">
            <ul>
                <li><a href="../../public/index.php">Home</a></li>
                <li><a href="../../public/about.php">About</a></li>
                <li><a href="registrar_dashboard.php">Dashboard</a></li>
                <li><a href="logout.php">Logout </a> <i class="fa-solid fa-user"></i></li>
            </ul>
            <!-- Add this button to toggle sidebar -->
            
        </div>
    </nav>
</head>
<body>
    <div class="container"><br>
        <h2 class="heading">Add New User</h2><br>
        <form action="create_user.php" method="post">
            <label for="student_id">User:</label>
            <input type="text" id="student_id" name="student_id" required>

            <label for="first_name">First Name:</label>
            <input type="text" id="first_name" name="first_name" required>

            <label for="last_name">Last Name:</label>
            <input type="text" id="last_name" name="last_name" required>

            <label for="new_password">Password:</label>
            <input type="password" id="new_password" name="new_password" required>

            <label for="role">Role:</label>
            <select id="role" name="role" required>
                <option value="student">Student</option>
                <option value="cashier">Cashier</option>
                <option value="registrar">Registrar</option>
            </select>

            <button type="submit">Create User</button>
            <a href="users.php" class="btn">Cancel</a>
        </form>
    </div>

    <footer>
        <div class="footer">
            <span>
                Copyright &#169; 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">
                    DBTI Website
                </a>
            </span>
        </div>
    </footer>
</body>
</html>
