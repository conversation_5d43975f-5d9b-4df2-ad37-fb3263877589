<?php
session_start();
require_once '../../config/db_conn.php';

if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../public/login.php");
    exit();
}

$sql = "SELECT * FROM payments WHERE student_id = ? ORDER BY payment_date DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Payment History</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .payment-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .payment-table th, .payment-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .payment-table th {
            background-color: #f4f4f4;
        }
        .amount-badge {
            padding: 5px 10px;
            border-radius: 4px;
            color: white;
            background-color: #4CAF50;
        }
        .transaction-id {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h2>Payment History</h2>
    <table class="payment-table">
        <thead>
            <tr>
                <th>Amount</th>
                <th>Date</th>
                <th>Transaction ID</th>
                <th>Payment Method</th>
            </tr>
        </thead>
        <tbody>
            <?php while($row = $result->fetch_assoc()): ?>
            <tr>
                <td><span class="amount-badge">K<?php echo number_format($row['amount'], 2); ?></span></td>
                <td><?php echo date('M d, Y H:i', strtotime($row['payment_date'])); ?></td>
                <td><span class="transaction-id"><?php echo htmlspecialchars($row['transaction_id']); ?></span></td>
                <td><?php echo ucfirst(htmlspecialchars($row['payment_method'])); ?></td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</body>
</html>
