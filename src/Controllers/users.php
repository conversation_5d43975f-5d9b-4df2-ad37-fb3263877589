<?php
session_start();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../../public/login.php");
    exit();
}

require_once '../../config/db_conn.php';

$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT u.*, s.first_name, s.last_name 
        FROM users u 
        LEFT JOIN students s ON u.student_id = s.student_id
        WHERE u.username LIKE ? 
        OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?
        ORDER BY u.user_id DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->bind_param("ss", $searchTerm, $searchTerm);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="DBTI Online Admin Dashboard">
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Admin Dashboard | DBTI Online</title>
    <link rel="stylesheet" href="styles.css">
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Search bar styling */
.search-bar {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    z-index: 999;
    background: white;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Top Container */
.top-container {
    width: 98%;
    margin: 90px auto 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Table Container */
.table-container {
    width: 98%;
    margin: 0 auto 100px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    overflow: auto;
    height: calc(100vh - 300px);
}

/* Table styling */
.user-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
}

.user-table thead tr {
    background-color: #4CAF50;
    color: white;
    font-weight: 600;
}

.user-table th {
    padding: 15px;
    text-align: left;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-table td {
    padding: 15px;
    border-bottom: 1px solid #edf2f7;
    font-size: 14px;
}

/* Navbar */
nav {
    background-color: #ffbf00;
    padding: 15px 4%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

nav .logo {
    height: 45px;
    width: auto;
}

nav .heading {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
}

nav ul {
    display: flex;
    gap: 15px;
    margin: 0;
    padding: 0;
}

nav ul li a {
    font-size: 0.9rem;
    padding: 8px 16px;
    background-color: #cc9900;
    border-radius: 4px;
    transition: all 0.2s ease;
    color: #fff;
    text-decoration: none;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 12px 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 1000;
}

footer .footer {
    max-width: 98%;
    margin: 0 auto;
    text-align: center;
    font-size: 14px;
}

footer a {
    color: #ffbf00;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

/* Buttons */
.btn {
    padding: 8px 15px;
    font-size: 14px;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
}

.delete-btn {
    background-color: #dc3545;
    margin-left: 8px;
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
    .container {
        padding: 10px;
        margin-bottom: 120px;
    }
    
    nav ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #ffbf00;
        padding: 10px 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    nav ul.active {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .hamburger {
        display: flex;
    }
}
</style>
</head>
<body>
<!-- Navigation Bar -->
<nav>
    <img src="img/logo.webp" alt="DBTI Logo" class="logo">
    <div class="heading">DBTI Online Registration</div>
    <div class="hamburger" onclick="toggleNavbar()">
        <div></div>
        <div></div>
        <div></div>
    </div>
    <ul id="navbar">
        <li><a href="../../public/index.php">Home</a></li>
        <li><a href="../../public/about.php">About</a></li>
        <li><a href="admin_dashboard.php">Dashboard</a></li>
        <li><a href="logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
    </ul>
</nav>

<div class="top-container">
    <form method="GET" class="search-form">
        <input type="text" name="search" placeholder="Search by username or name" value="<?= htmlspecialchars($search) ?>">
        <button type="submit">Search</button>
    </form>
    <a href="add_users.php" class="btn">Add New User</a>
</div>

<div class="table-container">
    <table class="user-table">
        <thead>
            <tr>
                <th>Full Name</th>
                <th>Username</th>
                <th>Role</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?= htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) ?></td>
                    <td><?= htmlspecialchars($row['username']) ?></td>
                    <td><?= htmlspecialchars($row['role']) ?></td>
                    <td data-label="Actions">
                        <button onclick="openEditModal(<?= $row['user_id'] ?>)" class="btn edit-btn">Edit</button>
                        <button onclick="confirmDelete(<?= $row['user_id'] ?>)" class="btn delete-btn">Delete</button>
                    </td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>

<!-- Modal for editing user password -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <h2>Edit User Password</h2>
        <form id="editForm" action="edit_user.php" method="POST">
            <input type="hidden" id="editUserId" name="user_id">
            <label for="new_password">New Password:</label>
            <input type="password" name="new_password" required>
            <button type="submit">Update Password</button>
            <button type="button" onclick="closeEditModal()">Cancel</button>
        </form>
    </div>
</div>

<footer>
    <div class="footer">
        <span>
            Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
            <a href="https://www.dbti.ac.pg/" target="_blank">
                DBTI Website
            </a>
        </span>
    </div>
</footer>
<script>
    function toggleNavbar() {
        const navbar = document.getElementById('navbar');
        navbar.classList.toggle('active');
    }
</script>
</body>
</html>
