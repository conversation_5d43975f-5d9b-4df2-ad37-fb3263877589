<?php
session_start();
require_once '../../config/db_conn.php';

// Check if user is logged in as student
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../public/login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quarterly Payment - DBTI Online Registration</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .payment-table {
            width: 80%;
            margin: 50px auto;
            border-collapse: collapse;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .payment-table, .payment-table th, .payment-table td {
            border: 1px solid #ddd;
        }

        .payment-table th {
            background-color: #f4f4f4;
            color: black;
            padding: 20px;
            text-align: center;
            font-size: 1.2em;
        }

        .payment-table td {
            padding: 20px;
            text-align: center;
            background-color: #fff;
        }

        .button-grid {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }

        .payment-button {
            padding: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            text-decoration: none;
            display: block;
            width: 100px;
            text-align: center;
            line-height: 1.2;
        }

        .payment-button:hover {
            background-color: #0056b3;
        }

        h2 {
            text-align: center;
        }





/* Navbar Base Styles */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
    width: 100%;
}

nav .logo {
    height: 50px;
    width: auto;
    margin-right: 15px;
}

nav .heading {
    font-size: 1.75rem;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
}

nav ul li a {
    font-size: 1rem;
    color: #fff;
    text-transform: uppercase;
    padding: 12px 20px;
    background-color: #cc9900;
    border-radius: 6px;
    font-weight: 700;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

nav ul li a:hover {
    background-color: #996600;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger div {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 4px 0;
    transition: 0.4s;
}

/* Mobile Responsive Styles */
@media only screen and (max-width: 768px) {
    nav {
        flex-wrap: wrap;
    }
    
    nav .logo {
        height: 40px;
    }
    
    nav ul {
        display: none;
        flex-direction: column;
        width: 100%;
        text-align: center;
        background-color: #ffbf00;
    }

    nav ul.active {
        display: flex;
    }

    nav ul li a {
        width: 200px;
        margin: 8px auto;
        text-align: center;
        display: block;
    }

    .hamburger {
        display: flex;
    }

    nav .heading {
        font-size: 1.2rem;
    }
}







    </style>
</head>
<body>
<nav>
    <img src="img/logo.webp" alt="DBTI Logo" class="logo">
    <div class="heading">DBTI Online Registration</div>
    <div class="hamburger" onclick="toggleNavbar()">
        <div></div>
        <div></div>
        <div></div>
    </div>
    <ul id="navbar">
        <li><a href="../../public/index.php">Home</a></li>
        <li><a href="../../public/about.php">About</a></li>
        <li><a href="student_dashboard.php">Dashboard</a></li>
        <li><a href="logout.php">Logout</a></li>
    </ul>
</nav>


    <div class="content-container">
        <br>
        <h2>Choose Your Quarterly Payment:</h2>

        <table class="payment-table">
            <thead>
                <tr>
                    <th>DIP</th>
                    <th>BTECH</th>
                    <th>BED</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div class="button-grid">
                            <a href="stripe/pay/dip_yr1_q1.php" class="payment-button">Year 1<br>Q1</a>
                            <a href="stripe/pay/dip_yr1_q2.php" class="payment-button">Year 1<br>Q2</a>
                            <a href="stripe/pay/dip_yr1_q3.php" class="payment-button">Year 1<br>Q3</a>
                            <a href="stripe/pay/dip_yr1_q4.php" class="payment-button">Year 1<br>Q4</a>
                            <a href="stripe/pay/dip_yr2_q1.php" class="payment-button">Year 2<br>Q1</a>
                            <a href="stripe/pay/dip_yr2_q2.php" class="payment-button">Year 2<br>Q2</a>
                            <a href="stripe/pay/dip_yr2_q3.php" class="payment-button">Year 2<br>Q3</a>
                            <a href="stripe/pay/dip_yr2_q4.php" class="payment-button">Year 2<br>Q4</a>
                            <a href="stripe/pay/dip_yr3_q1.php" class="payment-button">Year 3<br>Q1</a>
                            <a href="stripe/pay/dip_yr3_q2.php" class="payment-button">Year 3<br>Q2</a>
                            <a href="stripe/pay/dip_yr3_q3.php" class="payment-button">Year 3<br>Q3</a>
                            <a href="stripe/pay/dip_yr3_q4.php" class="payment-button">Year 3<br>Q4</a>
                        </div>
                    </td>
                    <td>
                        <div class="button-grid">
                            <a href="stripe/pay/btech_yr1_q1.php" class="payment-button">Year 1<br>Q1</a>
                            <a href="stripe/pay/btech_yr1_q2.php" class="payment-button">Year 1<br>Q2</a>
                            <a href="stripe/pay/btech_yr1_q3.php" class="payment-button">Year 1<br>Q3</a>
                            <a href="stripe/pay/btech_yr1_q4.php" class="payment-button">Year 1<br>Q4</a>
                            <a href="stripe/pay/btech_yr2_q1.php" class="payment-button">Year 2<br>Q1</a>
                            <a href="stripe/pay/btech_yr2_q2.php" class="payment-button">Year 2<br>Q2</a>
                            <a href="stripe/pay/btech_yr2_q3.php" class="payment-button">Year 2<br>Q3</a>
                            <a href="stripe/pay/btech_yr2_q4.php" class="payment-button">Year 2<br>Q4</a>
                            <a href="stripe/pay/btech_yr3_q1.php" class="payment-button">Year 3<br>Q1</a>
                            <a href="stripe/pay/btech_yr3_q2.php" class="payment-button">Year 3<br>Q2</a>
                            <a href="stripe/pay/btech_yr3_q3.php" class="payment-button">Year 3<br>Q3</a>
                            <a href="stripe/pay/btech_yr3_q4.php" class="payment-button">Year 3<br>Q4</a>
                            <a href="stripe/pay/btech_yr4_q1.php" class="payment-button">Year 4<br>Q1</a>
                            <a href="stripe/pay/btech_yr4_q2.php" class="payment-button">Year 4<br>Q2</a>
                            <a href="stripe/pay/btech_yr4_q3.php" class="payment-button">Year 4<br>Q3</a>
                            <a href="stripe/pay/btech_yr4_q4.php" class="payment-button">Year 4<br>Q4</a>
                        </div>
                    </td>
                    <td>
                        <div class="button-grid">
                            <a href="stripe/pay/bed_yr1_q1.php" class="payment-button">Year 1<br>Q1</a>
                            <a href="stripe/pay/bed_yr1_q2.php" class="payment-button">Year 1<br>Q2</a>
                            <a href="stripe/pay/bed_yr1_q3.php" class="payment-button">Year 1<br>Q3</a>
                            <a href="stripe/pay/bed_yr1_q4.php" class="payment-button">Year 1<br>Q4</a>
                            <a href="stripe/pay/bed_yr2_q1.php" class="payment-button">Year 2<br>Q1</a>
                            <a href="stripe/pay/bed_yr2_q2.php" class="payment-button">Year 2<br>Q2</a>
                            <a href="stripe/pay/bed_yr2_q3.php" class="payment-button">Year 2<br>Q3</a>
                            <a href="stripe/pay/bed_yr2_q4.php" class="payment-button">Year 2<br>Q4</a>
                            <a href="stripe/pay/bed_yr3_q1.php" class="payment-button">Year 3<br>Q1</a>
                            <a href="stripe/pay/bed_yr3_q2.php" class="payment-button">Year 3<br>Q2</a>
                            <a href="stripe/pay/bed_yr3_q3.php" class="payment-button">Year 3<br>Q3</a>
                            <a href="stripe/pay/bed_yr3_q4.php" class="payment-button">Year 3<br>Q4</a>
                            <a href="stripe/pay/bed_yr4_q1.php" class="payment-button">Year 4<br>Q1</a>
                            <a href="stripe/pay/bed_yr4_q2.php" class="payment-button">Year 4<br>Q2</a>
                            <a href="stripe/pay/bed_yr4_q3.php" class="payment-button">Year 4<br>Q3</a>
                            <a href="stripe/pay/bed_yr4_q4.php" class="payment-button">Year 4<br>Q4</a>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <?php include 'footer.php'; ?>

    <script>
    function toggleNavbar() {
        const navbar = document.getElementById('navbar');
        navbar.classList.toggle('active');
    }
</script>


</body>
</html>
