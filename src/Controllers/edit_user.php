<?php
// Define secure access constant for config.php
define('SECURE_ACCESS', true);

session_start();
require_once '../../config/db_conn.php';

if ($_SESSION['role'] != 'admin' || !isset($_POST['user_id'])) {
    header("Location: ../../public/login.php");
    exit();
}

$user_id = $_POST['user_id'];
$new_password = password_hash($_POST['new_password'], PASSWORD_DEFAULT);

$stmt = $conn->prepare("UPDATE users SET password = ? WHERE user_id = ?");
$stmt->bind_param("si", $new_password, $user_id);

if ($stmt->execute()) {
    header("Location: ../../admin/admin_dashboard.php?success=1");
} else {
    header("Location: ../../admin/admin_dashboard.php?error=1");
}
