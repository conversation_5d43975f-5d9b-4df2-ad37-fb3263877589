<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once '../../config/db_conn.php';
require_once '../../config/config.php';
require 'PHPMailer-6.9.2/src/PHPMailer.php';
require 'PHPMailer-6.9.2/src/SMTP.php';
require 'PHPMailer-6.9.2/src/Exception.php';
require 'fpdf186/fpdf.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Check if user is logged in and has registrar role
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar') {
    http_response_code(403);
    echo "Unauthorized access";
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['studentId'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        http_response_code(403);
        echo "Invalid request";
        exit();
    }

    // Sanitize inputs
    $studentId = sanitize_input($_POST['studentId']);
    $studentEmail = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $selectedCourses = isset($_POST['courses']) ? array_map('intval', $_POST['courses']) : [];

    // Fetch student information
    $student_sql = "SELECT * FROM students WHERE student_id = ?";
    $student_stmt = $conn->prepare($student_sql);
    $student_stmt->bind_param("s", $studentId);
    $student_stmt->execute();
    $student_result = $student_stmt->get_result();
    $student = $student_result->fetch_assoc();

    if (!$student) {
        echo "Student not found";
        exit();
    }

    // Create PDF
    $pdf = new FPDF();
    $pdf->AddPage();

    // Header
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 10, 'Don Bosco Technological Institute', 0, 1, 'C');
    $pdf->Cell(0, 10, 'Student Course Outline', 0, 1, 'C');
    $pdf->Ln(10);

    // Student Information
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->Cell(0, 8, 'Student Information:', 0, 1);
    $pdf->SetFont('Arial', '', 12);
    $pdf->Cell(0, 8, 'Name: ' . $student['first_name'] . ' ' . $student['last_name'], 0, 1);
    $pdf->Cell(0, 8, 'Student ID: ' . $student['student_id'], 0, 1);
    $pdf->Cell(0, 8, 'Program: ' . $student['program'], 0, 1);
    $pdf->Cell(0, 8, 'Technology: ' . $student['tech'], 0, 1);
    $pdf->Cell(0, 8, 'Year Level: ' . $student['year_level'], 0, 1);
    $pdf->Ln(10);

    // Course Table Header
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->Cell(30, 7, 'Course ID', 1, 0, 'C');
    $pdf->Cell(80, 7, 'Course Name', 1, 0, 'C');
    $pdf->Cell(40, 7, 'Semester', 1, 0, 'C');
    $pdf->Cell(40, 7, 'Year Level', 1, 1, 'C');

    // Course Table Content
    $pdf->SetFont('Arial', '', 12);
    foreach ($selectedCourses as $courseId) {
        $course_sql = "SELECT * FROM courses WHERE id = ?";
        $course_stmt = $conn->prepare($course_sql);
        $course_stmt->bind_param("i", $courseId);
        $course_stmt->execute();
        $course_result = $course_stmt->get_result();
        $course = $course_result->fetch_assoc();

        if ($course) {
            $pdf->Cell(30, 7, $course['course_id'], 1, 0, 'L');
            $pdf->Cell(80, 7, $course['course_name'], 1, 0, 'L');
            $pdf->Cell(40, 7, $course['semester'], 1, 0, 'L');
            $pdf->Cell(40, 7, $course['year_level'], 1, 1, 'L');
        }
    }

    // Generate a secure random filename for the PDF
    $pdf_file = TEMP_DIR . generate_secure_filename('pdf');
    $pdf->Output('F', $pdf_file);

    // Send Email using configuration from config.php
    $mail = new PHPMailer();
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = SMTP_AUTH;
        $mail->Username = REGISTRAR_EMAIL;
        $mail->Password = REGISTRAR_PASSWORD;
        $mail->SMTPSecure = SMTP_SECURE;
        $mail->Port = SMTP_PORT;

        // Recipients
        $mail->setFrom(REGISTRAR_EMAIL, 'DBTI ONLINE REGISTRATION');
        $mail->addAddress($studentEmail, $student['first_name'] . ' ' . $student['last_name']);

        // Attach PDF
        $mail->addAttachment($pdf_file, 'Course_Registration.pdf');

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Course Registration - ' . $student['first_name'] . ' ' . $student['last_name'];
        $mail->Body = 'Dear ' . $student['first_name'] . ' ' . $student['last_name'] . ',<br><br>' .
                     'Thank you for your registration. Please find attached your course registration details.<br><br>' .
                     'Regards,<br>Registrar, DON BOSCO TECHNOLOGICAL INSTITUTE';

        $mail->send();
        echo 'Course registration sent successfully to ' . htmlspecialchars($studentEmail);
    } catch (Exception $e) {
        echo 'Message could not be sent. Mailer Error: ' . $mail->ErrorInfo;
    }

    // Clean up PDF file
    if (file_exists($pdf_file)) {
        unlink($pdf_file);
    }
    exit();
}

// If not POST request, show the form
// Get student ID and email from URL parameters
$studentId = isset($_GET['studentId']) ? $_GET['studentId'] : '';
$studentEmail = isset($_GET['email']) ? $_GET['email'] : '';

// Fetch student information
$student_sql = "SELECT * FROM students WHERE student_id = ?";
$student_stmt = $conn->prepare($student_sql);
$student_stmt->bind_param("s", $studentId);
$student_stmt->execute();
$student_result = $student_stmt->get_result();
$student = $student_result->fetch_assoc();

if (!$student) {
    echo "Student not found";
    exit();
}

// Fetch available courses
$courses_sql = "SELECT * FROM courses WHERE program = ? AND technology = ? AND year_level = ? ORDER BY semester, course_id";
$courses_stmt = $conn->prepare($courses_sql);
$courses_stmt->bind_param("sss", $student['program'], $student['tech'], $student['year_level']);
$courses_stmt->execute();
$courses_result = $courses_stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Send Course Registration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        .course-list { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
        .course-item { padding: 5px 0; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <div class="container">
        <h2>Send Course Registration</h2>
        <form method="POST">
            <!-- CSRF Protection -->
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

            <div class="form-group">
                <label>Student Name: <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></label>
            </div>
            <div class="form-group">
                <label>Student ID: <?php echo htmlspecialchars($student['student_id']); ?></label>
                <input type="hidden" name="studentId" value="<?php echo htmlspecialchars($student['student_id']); ?>">
                <input type="hidden" name="email" value="<?php echo htmlspecialchars($studentEmail); ?>">
            </div>
            <div class="form-group">
                <label>Courses:</label>
                <div class="course-list">
                    <?php while ($course = $courses_result->fetch_assoc()): ?>
                        <div class="course-item">
                            <label>
                                <input type="checkbox" name="courses[]" value="<?php echo $course['id']; ?>" checked>
                                <?php echo htmlspecialchars($course['course_id'] . ' - ' . $course['course_name'] . ' (' . $course['semester'] . ')'); ?>
                            </label>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
            <button type="submit">Send Course Registration</button>
        </form>
    </div>
</body>
</html>
