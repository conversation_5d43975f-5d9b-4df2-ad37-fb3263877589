<?php
// Define secure access constant for config.php
define('SECURE_ACCESS', true);

session_start();
require_once '../../config/db_conn.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['user_id'])) {
    $user_id = $_POST['user_id'];

    // First check if the user is an admin
    $check_sql = "SELECT role FROM users WHERE user_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $user_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    $user = $result->fetch_assoc();

    if ($user['role'] === 'admin') {
        echo 'protected';
        exit;
    }

    // If not admin, proceed with deletion
    $sql = "DELETE FROM users WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);

    if ($stmt->execute()) {
        echo 'success';
    } else {
        echo 'error';
    }
}
