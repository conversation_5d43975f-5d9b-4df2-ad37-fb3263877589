<?php
// Define secure access constant for config.php
define('SECURE_ACCESS', true);

session_start();
if ($_SESSION['role'] != 'admin') {
    header("Location: ../../public/login.php");
    exit();
}

require_once '../../config/db_conn.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: ../../admin/admin_dashboard.php?error=invalid_request");
    exit();
}

// Validate required fields
if (empty($_POST['student_id']) || empty($_POST['first_name']) || empty($_POST['last_name']) ||
    empty($_POST['new_password']) || empty($_POST['role'])) {
    header("Location: ../../admin/admin_dashboard.php?error=missing_fields");
    exit();
}

$student_id = trim($_POST['student_id']);
$first_name = trim($_POST['first_name']);
$last_name = trim($_POST['last_name']);
$new_password = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
$role = $_POST['role'];

// Validate role
$valid_roles = ['student', 'cashier', 'registrar', 'admin'];
if (!in_array($role, $valid_roles)) {
    header("Location: ../../admin/admin_dashboard.php?error=invalid_role");
    exit();
}

$conn->begin_transaction();

try {
    // Check if student_id already exists in students table
    $check_stmt = $conn->prepare("SELECT student_id FROM students WHERE student_id = ?");
    $check_stmt->bind_param("s", $student_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows > 0) {
        $check_stmt->close();
        $conn->rollback();
        header("Location: ../../admin/admin_dashboard.php?error=student_exists");
        exit();
    }
    $check_stmt->close();

    // Check if username already exists in users table
    $check_stmt = $conn->prepare("SELECT username FROM users WHERE username = ?");
    $check_stmt->bind_param("s", $student_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows > 0) {
        $check_stmt->close();
        $conn->rollback();
        header("Location: ../../admin/admin_dashboard.php?error=username_exists");
        exit();
    }
    $check_stmt->close();

    // Insert into students table with required fields
    $stmt = $conn->prepare("INSERT INTO students (student_id, first_name, last_name, gender, phone_number, residential_address, home_province, guardian_name, guardian_occupation, guardian_phone_number, guardian_email, student_email, dob, program, tech) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    if (!$stmt) {
        throw new Exception("Prepare failed for students table: " . $conn->error);
    }

    // Set default values for required fields
    $default_gender = 'Other';
    $default_phone = '000-0000';
    $default_address = 'DBTI Campus';
    $default_province = 'National Capital District';
    $default_guardian_name = 'Not Provided';
    $default_guardian_occupation = 'Not Provided';
    $default_guardian_phone = '000-0000';
    $default_guardian_email = '<EMAIL>';
    $default_student_email = $student_id . '@dbti.ac.pg';
    $default_dob = '2000-01-01';
    $default_program = 'Bachelor in Technology';
    $default_tech = 'Information Technology';

    $stmt->bind_param("sssssssssssssss",
        $student_id, $first_name, $last_name, $default_gender, $default_phone,
        $default_address, $default_province, $default_guardian_name, $default_guardian_occupation,
        $default_guardian_phone, $default_guardian_email, $default_student_email, $default_dob,
        $default_program, $default_tech
    );

    if (!$stmt->execute()) {
        throw new Exception("Execute failed for students table: " . $stmt->error);
    }
    $stmt->close();

    // Insert into users table
    $stmt = $conn->prepare("INSERT INTO users (username, student_id, password, role) VALUES (?, ?, ?, ?)");
    if (!$stmt) {
        throw new Exception("Prepare failed for users table: " . $conn->error);
    }
    $stmt->bind_param("ssss", $student_id, $student_id, $new_password, $role);
    if (!$stmt->execute()) {
        throw new Exception("Execute failed for users table: " . $stmt->error);
    }
    $stmt->close();

    $conn->commit();
    header("Location: ../../admin/admin_dashboard.php?success=user_created");
    exit();

} catch (Exception $e) {
    $conn->rollback();
    error_log("User creation error: " . $e->getMessage());
    header("Location: ../../admin/admin_dashboard.php?error=creation_failed&msg=" . urlencode($e->getMessage()));
    exit();
}

$conn->close();
?>