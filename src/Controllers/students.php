<?php
session_start();
require_once '../../config/db_conn.php';

if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../../public/login.php");
    exit();
}

$sql = "SELECT * FROM students ORDER BY student_id";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    
    <meta charset="UTF-8">
    <title>Student Year Levels</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .student-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .student-table th, .student-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .student-table th {
            background-color: #f4f4f4;
        }
        .year-badge {
            padding: 5px 10px;
            border-radius: 4px;
            color: white;
        }
        .year-1 { background-color: #4CAF50; }
        .year-2 { background-color: #2196F3; }
        .year-3 { background-color: #ff9800; }
        .year-4 { background-color: #f44336; }
        .temporary { background-color: #9e9e9e; }
    </style>
</head>
<body>
    <h2>Student Year Levels</h2>
    <table class="student-table">
        <thead>
            <tr>
                <th>Student ID</th>
                <th>Name</th>
                <th>Year Level</th>
                <th>Program</th>
                <th>Tech</th>
            </tr>
        </thead>
        <tbody>
            <?php while($row = $result->fetch_assoc()): 
                $yearLevelClass = strtolower(str_replace(' ', '-', $row['year_level']));
            ?>
            <tr>
                <td><?php echo htmlspecialchars($row['student_id']); ?></td>
                <td><?php echo htmlspecialchars($row['first_name'] . ' ' . $row['last_name']); ?></td>
                <td><span class="year-badge <?php echo $yearLevelClass; ?>">
                    <?php echo htmlspecialchars($row['year_level']); ?>
                </span></td>
                <td><?php echo htmlspecialchars($row['program']); ?></td>
                <td><?php echo htmlspecialchars($row['tech']); ?></td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</body>
</html>
