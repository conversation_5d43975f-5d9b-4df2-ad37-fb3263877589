<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration
require_once '../../config/config.php';

// Session timeout check using the function from security_helpers.php
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: ../../public/login.php");
    exit();
}

// For debugging, temporarily allow access without session check
$debug_mode = false;

if (!$debug_mode && (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar')) {
    header("Location: ../../public/login.php");
    exit();
}

// Simple error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
require_once '../../config/db_conn.php';

// Check if course_data.php exists and include it safely
if (file_exists('course_data.php')) {
    include 'course_data.php';
} else {
    $error_message = "Error: course_data.php file not found";
    $courses = [];
}

// Initialize arrays
$db_courses = [];
$file_courses = [];
$missing_courses = [];
$extra_courses = [];

// Try to get courses from database
try {
    // Make sure $conn exists
    if (!isset($conn) || $conn === null) {
        throw new Exception("Database connection failed - connection variable is null");
    }

    $sql = "SELECT course_id, course_name, program, technology, year_level, semester FROM courses ORDER BY program, year_level, semester";
    $result = $conn->query($sql);

    if ($result === false) {
        throw new Exception("Database query failed: " . $conn->error);
    }

    // Store database courses in array
    if ($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $key = $row['course_id'].'-'.$row['program'].'-'.$row['year_level'].'-'.$row['semester'];
            $db_courses[$key] = $row;
        }
    }
} catch (Exception $e) {
    $db_error = "Database error: " . $e->getMessage();
}

// Process courses from course_data.php if available
if (isset($courses) && is_array($courses)) {
    foreach ($courses as $program => $technologies) {
        if (!is_array($technologies)) continue;

        foreach ($technologies as $technology => $years) {
            if (!is_array($years)) continue;

            foreach ($years as $year => $course_list) {
                if (!is_array($course_list)) continue;

                foreach ($course_list as $course_id => $course_name) {
                    // Determine semester based on course_id
                    $semester = (substr($course_id, -1) == '1') ? 'Semester 1' : 'Semester 2';

                    $key = $course_id.'-'.$program.'-'.$year.'-'.$semester;
                    $file_courses[$key] = [
                        'course_id' => $course_id,
                        'course_name' => $course_name,
                        'program' => $program,
                        'technology' => $technology,
                        'year_level' => $year,
                        'semester' => $semester
                    ];

                    // Check if course exists in database
                    if (!isset($db_courses[$key])) {
                        $missing_courses[] = $file_courses[$key];
                    }
                }
            }
        }
    }
}

// Find courses in database but not in file
foreach ($db_courses as $key => $course) {
    if (!isset($file_courses[$key])) {
        $extra_courses[] = $course;
    }
}

// Count statistics
$total_file_courses = count($file_courses);
$total_db_courses = count($db_courses);
$total_missing = count($missing_courses);
$total_extra = count($extra_courses);
$is_synced = ($total_missing == 0 && $total_extra == 0);

// Count programs and technologies
$file_programs = [];
$db_programs = [];
$file_technologies = [];
$db_technologies = [];

// Process file courses
foreach ($file_courses as $course) {
    if (!in_array($course['program'], $file_programs)) {
        $file_programs[] = $course['program'];
    }
    if (!in_array($course['technology'], $file_technologies)) {
        $file_technologies[] = $course['technology'];
    }
}

// Process database courses
foreach ($db_courses as $course) {
    if (!in_array($course['program'], $db_programs)) {
        $db_programs[] = $course['program'];
    }
    if (!in_array($course['technology'], $db_technologies)) {
        $db_technologies[] = $course['technology'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Course Database Verification - DBTI Online Registration">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Course Database Verification | DBTI Online</title>
    <style>
        /* Core Styles */
        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            background-color: #f5f5f5;
            scroll-behavior: smooth;
            padding-bottom: 60px;
            margin: 0;
        }

        /* Navigation Bar */
        nav {
            background-color: #ffbf00;
            padding: 15px 5%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
            width: 100%;
        }

        nav .logo {
            height: 50px;
            width: auto;
            margin-right: 15px;
        }

        nav .heading {
            font-size: 1.75rem;
            font-weight: bold;
            color: #fff;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 20px;
        }

        nav ul li a {
            color: #fff;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        nav ul li a:hover {
            background-color: rgba(255,255,255,0.2);
        }

        /* Mobile Navigation */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 3px 0;
            transition: 0.4s;
        }

        /* Main content area */
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        /* Info box styling */
        .info-box {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
        }

        .info-box h3 {
            display: flex;
            align-items: center;
            margin-top: 0;
            color: #333;
        }

        .info-box h3 i {
            margin-right: 10px;
            color: #ffbf00;
        }

        /* Stats and metrics */
        .stats-count {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }

        .stats-count .number {
            font-weight: bold;
            font-size: 1.1em;
        }

        .stats-count .warning {
            color: #e67e22;
        }

        .stats-count .danger {
            color: #e74c3c;
        }

        /* Course list styling */
        .course-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .course-item {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .missing-course {
            border-left: 4px solid #e74c3c;
        }

        .course-code {
            font-weight: bold;
            font-family: monospace;
            background: #333;
            color: white;
            padding: 3px 6px;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 10px;
        }

        /* Action buttons */
        .action-button {
            display: inline-flex;
            align-items: center;
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            margin-top: 15px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-button i {
            margin-right: 8px;
        }

        .action-button:hover {
            background: #43A047;
            transform: translateY(-2px);
        }

        .action-button.warning {
            background: #f44336;
        }

        .action-button.warning:hover {
            background: #d32f2f;
        }

        /* Footer styling */
        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 15px;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ffbf00;
            text-decoration: none;
        }

        /* Status indicators */
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .warning {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        /* Responsive adjustments */
        @media only screen and (max-width: 768px) {
            .hamburger {
                display: flex;
            }

            nav ul {
                position: fixed;
                right: -100%;
                top: 70px;
                flex-direction: column;
                background: #ffbf00;
                width: 100%;
                text-align: center;
                transition: 0.3s;
                box-shadow: 0 10px 10px rgba(0,0,0,0.1);
                padding: 20px 0;
                z-index: 1000;
            }

            nav ul.active {
                right: 0;
            }

            nav .logo {
                height: 40px;
            }

            nav .heading {
                font-size: 1.2rem;
            }

            .course-list {
                grid-template-columns: 1fr;
            }
        }

        /* Debugging styles */
        .debug-info {
            background: #ffe;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav>
        <div style="display: flex; align-items: center;">
            <img src="img/logo.webp" alt="DBTI Logo" class="logo">
            <div class="heading">DBTI Online Registration</div>
        </div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="../../public/index.php">Home</a></li>
            <li><a href="../../public/about.php">About</a></li>
            <li><a href="registrar_dashboard.php">Dashboard</a></li>
            <li><a href="logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
        </ul>
    </nav>

    <div class="container">
        <h1>Course Database Verification</h1>

        <?php if ($debug_mode): ?>
            <div class="debug-info">
                <h3>Debug Information:</h3>
                <p>Database connection: <?php echo isset($conn) ? "Connected" : "Not connected"; ?></p>
                <p>courses variable: <?php echo isset($courses) ? "Set" : "Not set"; ?></p>
                <p>courses count: <?php echo isset($courses) ? count($courses) : "N/A"; ?></p>
                <p>DB courses count: <?php echo count($db_courses); ?></p>
                <p>File courses count: <?php echo count($file_courses); ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($db_error)): ?>
            <div class="warning">
                <p><strong>Database Error:</strong> <?php echo $db_error; ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="warning">
                <p><strong>System Error:</strong> <?php echo $error_message; ?></p>
            </div>
        <?php endif; ?>

        <!-- Summary Information -->
        <div class="info-box">
            <h3><i class="fa-solid fa-info-circle"></i> Database Summary</h3>
            <p>Compare courses between course_data.php and the database.</p>

            <div class="stats-count">
                Programs in course file: <span class="number"><?php echo count($file_programs); ?></span><br>
                Programs in database: <span class="number <?php echo (count($file_programs) != count($db_programs)) ? 'warning' : ''; ?>"><?php echo count($db_programs); ?></span>
            </div>

            <div class="stats-count">
                Courses in file: <span class="number"><?php echo $total_file_courses; ?></span><br>
                Courses in database: <span class="number <?php echo ($total_file_courses != $total_db_courses) ? 'warning' : ''; ?>"><?php echo $total_db_courses; ?></span>
            </div>
        </div>

        <!-- Missing Courses -->
        <?php if ($total_missing > 0): ?>
            <div class="info-box">
                <h3><i class="fa-solid fa-triangle-exclamation"></i> Missing Courses</h3>
                <p>These courses are in the course file but missing from the database.</p>

                <div class="stats-count">
                    Missing courses: <span class="number danger"><?php echo $total_missing; ?></span>
                </div>

                <div class="course-list">
                    <?php foreach ($missing_courses as $course): ?>
                        <div class="course-item missing-course">
                            <p class="course-code"><?php echo htmlspecialchars($course['course_id']); ?></p>
                            <p><?php echo htmlspecialchars($course['course_name']); ?></p>
                            <p><small>Program: <?php echo htmlspecialchars($course['program']); ?></small></p>
                            <p><small>Technology: <?php echo htmlspecialchars($course['technology']); ?></small></p>
                            <p><small>Year: <?php echo htmlspecialchars($course['year_level']); ?>, <?php echo htmlspecialchars($course['semester']); ?></small></p>
                        </div>
                    <?php endforeach; ?>
                </div>

                <a href="sync_courses.php?action=add_missing" class="action-button">
                    <i class="fa-solid fa-plus"></i> Add Missing Courses
                </a>
            </div>
        <?php endif; ?>

        <!-- Extra Courses -->
        <?php if ($total_extra > 0): ?>
            <div class="info-box">
                <h3><i class="fa-solid fa-circle-exclamation"></i> Extra Courses</h3>
                <p>These courses are in the database but not in the course file.</p>

                <div class="stats-count">
                    Extra courses: <span class="number warning"><?php echo $total_extra; ?></span>
                </div>

                <div class="course-list">
                    <?php foreach ($extra_courses as $course): ?>
                        <div class="course-item">
                            <p class="course-code"><?php echo htmlspecialchars($course['course_id']); ?></p>
                            <p><?php echo htmlspecialchars($course['course_name']); ?></p>
                            <p><small>Program: <?php echo htmlspecialchars($course['program']); ?></small></p>
                            <p><small>Technology: <?php echo htmlspecialchars($course['technology']); ?></small></p>
                            <p><small>Year: <?php echo htmlspecialchars($course['year_level']); ?>, <?php echo htmlspecialchars($course['semester']); ?></small></p>
                        </div>
                    <?php endforeach; ?>
                </div>

                <a href="sync_courses.php?action=remove_extra" class="action-button warning">
                    <i class="fa-solid fa-trash"></i> Remove Extra Courses
                </a>
            </div>
        <?php endif; ?>

        <!-- Synchronized Courses -->
        <?php if ($is_synced): ?>
            <div class="info-box">
                <h3><i class="fa-solid fa-check"></i> All Courses in Sync</h3>
                <p>All courses are properly synchronized between the course file and database.</p>

                <div class="stats-count">
                    Total courses: <span class="number"><?php echo $total_file_courses; ?></span>
                </div>

                <a href="manage_courses.php" class="action-button">
                    <i class="fa-solid fa-book"></i> Manage Courses
                </a>
            </div>
        <?php endif; ?>

        <div class="info-box">
            <h3><i class="fa-solid fa-wrench"></i> Course Database Tools</h3>
            <p>Use these tools to fix the course database.</p>

            <div style="margin-top: 15px;">
                <a href="sync_courses.php" class="action-button">
                    <i class="fas fa-sync"></i> Sync Courses to Database
                </a>

                <a href="manage_courses.php" class="action-button" style="margin-left: 10px;">
                    <i class="fas fa-cog"></i> Manage Courses
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="footer">
            <span>
                Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
            </span>
        </div>
    </footer>

    <script>
        // Simple toggle for mobile navigation
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }

        // Make sure document is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Document loaded successfully');
        });
    </script>
</body>
</html>