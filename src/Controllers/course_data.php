<?php
$courses = [
    'Bachelor in Technology' => [
        'Information Technology' => [
            'Year 1' => [
                'INF121' => 'Introduction to Programming (3 units)',
                'INF122' => 'Computer Fundamentals (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'INF221' => 'Database Systems (3 units)',
                'INF222' => 'Web Development (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'INF321' => 'Software Engineering (3 units)',
                'INF322' => 'Network Administration (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'INF421' => 'Wide Area Network (3 units)',
                'INF422' => 'Electronic Commerce (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ],
        'Automotive Technology' => [
            'Year 1' => [
                'AUT121' => 'Basic Automotive Systems (3 units)',
                'AUT122' => 'Automotive Tools and Equipment (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'AUT221' => 'Engine Systems (3 units)',
                'AUT222' => 'Electrical Systems (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'AUT321' => 'Automotive Systems (3 units)',
                'AUT322' => 'Vehicle Maintenance (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'AUT421' => 'Advanced Automotive Systems (3 units)',
                'AUT422' => 'Vehicle Diagnostics (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ],
        'Instrumentation Technology' => [
            'Year 1' => [
                'INS121' => 'Basic Instrumentation (3 units)',
                'INS122' => 'Instrumentation Tools (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'INS221' => 'Instrumentation Systems (3 units)',
                'INS222' => 'Measurement Techniques (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'INS321' => 'Advanced Measurement (3 units)',
                'INS322' => 'Control Systems (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'INS421' => 'Process Control Systems (3 units)',
                'INS422' => 'Instrumentation Project (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ],
        'Electronics Technology' => [
            'Year 1' => [
                'ELE121' => 'Electronic Circuits (3 units)',
                'ELE122' => 'Digital Electronics (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'ELE221' => 'Microcontroller Programming (3 units)',
                'ELE222' => 'Analog Electronics (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'ELE321' => 'Advanced Electronics (3 units)',
                'ELE322' => 'Communication Systems (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'ELE421' => 'Embedded Systems (3 units)',
                'ELE422' => 'Capstone Project (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ],
        'Electrical Technology' => [
            'Year 1' => [
                'ETL121' => 'Electrical Basics (3 units)',
                'ETL122' => 'Circuit Analysis (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'ETL221' => 'Power Systems (3 units)',
                'ETL222' => 'Control Systems (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'ETL321' => 'Electrical Machines (3 units)',
                'ETL322' => 'Industrial Electronics (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'ETL421' => 'Advanced Electrical Systems (3 units)',
                'ETL422' => 'Capstone Project (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ],
        'Metal Welding & Fabrication Technology' => [
            'Year 1' => [
                'MWF121' => 'Introduction to Welding (3 units)',
                'MWF122' => 'Fabrication Basics (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'MWF221' => 'Advanced Welding (3 units)',
                'MWF222' => 'Metal Fabrication Techniques (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'MWF321' => 'Welding Systems (3 units)',
                'MWF322' => 'Fabrication Project (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'MWF421' => 'Advanced Welding Processes (3 units)',
                'MWF422' => 'Capstone Fabrication Project (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ],
        'Maintenance Fitting & Machining Technology' => [
            'Year 1' => [
                'MFM121' => 'Machining Basics (3 units)',
                'MFM122' => 'Basic Fitting Techniques (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'MFM221' => 'Machining Systems (3 units)',
                'MFM222' => 'Fitting Advanced Techniques (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'MFM321' => 'Machining Processes (3 units)',
                'MFM322' => 'Capstone Fitting Project (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ],
            'Year 4' => [
                'MFM421' => 'Advanced Machining Techniques (3 units)',
                'MFM422' => 'Capstone Project (3 units)',
                'PTR421' => 'Supervision in Workplace (3 units)',
                'PTR422' => 'Project Study (3 units)',
                'THE421' => 'Theology 7 (3 units)',
                'THE422' => 'Theology 8 (3 units)'
            ]
        ]
    ],
    'Diploma In Technology' => [
        'Information Technology' => [
            'Year 1' => [
                'DINF121' => 'Introduction to Programming (3 units)',
                'DINF122' => 'Computer Fundamentals (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'DINF221' => 'Database Systems (3 units)',
                'DINF222' => 'Web Development (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'DINF321' => 'Software Engineering (3 units)',
                'DINF322' => 'Network Administration (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ]
        ],
        'Automotive Technology' => [
            'Year 1' => [
                'DAUT121' => 'Basic Automotive Systems (3 units)',
                'DAUT122' => 'Automotive Tools and Equipment (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'DAUT221' => 'Engine Systems (3 units)',
                'DAUT222' => 'Electrical Systems (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'DAUT321' => 'Automotive Systems (3 units)',
                'DAUT322' => 'Vehicle Maintenance (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ]
        ],
        'Instrumentation Technology' => [
            'Year 1' => [
                'DIN121' => 'Basics of Instrumentation (3 units)',
                'DIN122' => 'Instrumentation Tools and Practices (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'DIN221' => 'Introduction to Measurement (3 units)',
                'DIN222' => 'Instrumentation Devices (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'DIN321' => 'Instrumentation Systems (3 units)',
                'DIN322' => 'Control Systems (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ]
        ],
        'Electronics Technology' => [
            'Year 1' => [
                'DELE121' => 'Electronic Circuits (3 units)',
                'DELE122' => 'Digital Electronics (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'DELE221' => 'Microcontroller Programming (3 units)',
                'DELE222' => 'Analog Electronics (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'DELE321' => 'Advanced Electronics (3 units)',
                'DELE322' => 'Communication Systems (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ]
        ],
        'Electrical Technology' => [
            'Year 1' => [
                'DETL121' => 'Electrical Basics (3 units)',
                'DETL122' => 'Circuit Analysis (3 units)',
                'PTR121' => 'Basic Workshop Practice (3 units)',
                'PTR122' => 'Technical Drawing (3 units)',
                'THE121' => 'Theology 1 (3 units)',
                'THE122' => 'Theology 2 (3 units)'
            ],
            'Year 2' => [
                'DETL221' => 'Power Systems (3 units)',
                'DETL222' => 'Control Systems (3 units)',
                'PTR221' => 'Professional Practice (3 units)',
                'PTR222' => 'Technical Writing (3 units)',
                'THE221' => 'Theology 3 (3 units)',
                'THE222' => 'Theology 4 (3 units)'
            ],
            'Year 3' => [
                'DETL321' => 'Power Distribution (3 units)',
                'DETL322' => 'Automation and Control (3 units)',
                'PTR321' => 'Industrial Relations (3 units)',
                'PTR322' => 'Project Management (3 units)',
                'THE321' => 'Theology 5 (3 units)',
                'THE322' => 'Theology 6 (3 units)'
            ]
        ]
    ]
];
?>
