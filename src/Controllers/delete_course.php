<?php
session_start();
require_once '../../config/db_conn.php';

// Check if user is authorized
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['course_id'])) {
    $course_id = $_POST['course_id'];
    
    try {
        // Delete from database
        $stmt = $conn->prepare("DELETE FROM courses WHERE course_id = ?");
        $stmt->bind_param("s", $course_id);
        
        if ($stmt->execute()) {
            // Success - course deleted from database
            // Note: We should also update course_data.php but that's complex
            // Just note that a sync will be needed
            echo json_encode([
                'success' => true, 
                'message' => 'Course deleted from database. Run a sync to update course_data.php.'
            ]);
        } else {
            throw new Exception("Database error: " . $stmt->error);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}
?> 