<?php
// Start output buffering
ob_start();

// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration and database connection
require_once '../../config/config.php';
require_once '../../config/db_conn.php';

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session timeout check
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: ../../public/login.php");
    exit();
}

// Update last activity time
$_SESSION['last_activity'] = time();

// Redirect if user is not a student
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    header("Location: ../../public/login.php");
    exit();
}

// Fetch user information
$sql = "SELECT * FROM users WHERE username = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    echo "No user found with username: " . htmlspecialchars($_SESSION['username']);
    exit;
}

// Fetch payment information
$payment_sql = "SELECT SUM(amount) as total_paid FROM payments WHERE student_id = ?";
$payment_stmt = $conn->prepare($payment_sql);
$payment_stmt->bind_param("s", $_SESSION['username']);
$payment_stmt->execute();
$payment_result = $payment_stmt->get_result();
$payment_data = $payment_result->fetch_assoc();
$total_paid = $payment_data['total_paid'] ?? 0;

// Define required fee amount for registration
$required_fee = 2440;

// Check payment status
$has_paid = ($total_paid >= $required_fee);

// Fetch student information
$student_sql = "SELECT * FROM students WHERE student_id = ?";
$student_stmt = $conn->prepare($student_sql);
$student_stmt->bind_param("s", $_SESSION['username']);
$student_stmt->execute();
$student_result = $student_stmt->get_result();
$student_data = $student_result->fetch_assoc();

// Add this near the beginning of the file where you fetch student data
$debug = false; // Set to true to see debug info

// Add debug info if needed
if ($debug) {
    echo '<div style="margin: 20px; padding: 20px; background: #f8f9fa; border: 1px solid #ddd;">';
    echo '<h3>Debug Info</h3>';
    echo '<p>Student ID: ' . $_SESSION['username'] . '</p>';
    echo '<p>Registration Status: ' . ($student_data['registration_status'] ?? 'Not set') . '</p>';
    echo '</div>';
}

// Add payment history function here
function getPaymentHistory($student_id) {
    require 'db_conn.php';
    $sql = "SELECT amount, payment_date, transaction_id, payment_method
            FROM payments
            WHERE student_id = ?
            ORDER BY payment_date DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $payments = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    $conn->close();
    return $payments;
}

// Close statements and connection
$stmt->close();
$payment_stmt->close();
$student_stmt->close();
$conn->close();
?>


<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="stylesheet" href="student_dashboard.css" />

    <title>DBTI Registration System</title>
    <link rel="icon" href="img/logo.png" type="image/png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            background-color: #f5f5f5;
            scroll-behavior: smooth;
        }

        /* Navbar */
        nav {
            background-color: #ffbf00;
            padding: 20px 5%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        nav .heading {
            font-size: 1.75rem;
            font-weight: bold;
            color: #fff;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 20px;
        }

        nav ul li a {
            font-size: 1rem;
            color: #fff;
            text-transform: uppercase;
            padding: 12px 20px;
            background-color: #cc9900;
            border-radius: 6px;
            font-weight: 700;
            transition: all 0.3s ease;
            display: inline-block;
        }

        nav ul li a:hover {
            background-color: #996600;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        nav .logo {
            height: 50px;
            width: auto;
            margin-right: 15px;
        }

        /* Hamburger Menu */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 4px 0;
            transition: 0.4s;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            height: calc(100vh - 250px); /* Shorter height */
            background-color: #ffffff;
            padding: 20px;
            position: fixed;
            top: 60px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            overflow-y: auto;
            margin-bottom: 60px;
        }

        .sidebar a {
            color: #000000;
            font-size: 1.2rem;
            padding: 15px 10px;
            display: block;
            margin: 10px 0;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar hr {
            margin: 15px 0;
            border: none;
            border-top: 1px solid #e0e0e0;
        }

        .sidebar i {
            margin-right: 10px;
            font-size: 1.1rem;
        }

        .sidebar a:hover {
            background-color: #f5f5f5;
            transform: translateX(5px);
        }
        .content-box {
            margin-left: 270px;
            padding: 20px;
            background-color: #e3f2fd;
            min-height: calc(100vh - 80px);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .payment-info {
            background: white;
            padding: 30px 40px;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
            width: 80%;
            max-width: 600px;
        }

        .payment-info h3 {
            font-size: 2.2rem;
            color: #2c3e50;
            margin-bottom: 25px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .payment-info p {
            font-size: 1.4rem;
            line-height: 1.8;
            margin: 15px 0;
            color: #34495e;
            font-weight: 500;
        }

        .content-box h2 {
            font-size: 2.5rem;
            color: #1a237e;
            margin-bottom: 20px;
            font-weight: 800;
            text-transform: capitalize;
        }
        /* Footer */
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 15px 0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ffbf00;
            text-decoration: none;
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            nav {
                flex-wrap: wrap;
            }

            nav .logo {
                height: 40px;
            }

            nav ul {
                display: none;
                flex-direction: column;
                width: 100%;
                text-align: center;
                background-color: #ffbf00;
            }

            nav ul.active {
                display: flex;
            }

            nav ul li a {
                width: 200px;
                margin: 8px auto;
                text-align: center;
                display: block;
            }

            .hamburger {
                display: flex;
            }

            nav .heading {
                font-size: 1.2rem;
            }

            .sidebar {
                width: 100%;
                position: static;
                height: auto;
                margin-bottom: 20px;
            }

            .content-box {
                margin-left: 0;
                padding: 15px;
            }
        }



        .student-info-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
    padding: 20px;
}

.student-info-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    position: relative;
    overflow-y: auto;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
    padding-right: 10px;
}

.close-btn {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    cursor: pointer;
    color: #666;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-item .label {
    display: block;
    font-weight: 700;
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 5px;
}

.info-item .value {
    display: block;
    font-size: 1.2rem;
    color: #34495e;
}

.student-info-content h2 {
    font-size: 2rem;
    color: #1a237e;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 800;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.dropdown {
    position: relative;
    z-index: 9999;
}

.dropdown-menu {
    display: none;
    position: absolute;
    left: 0;
    top: 100%;
    background: white;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    z-index: 9999;
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu a {
    padding: 12px 20px;
    font-size: 1rem;
    border-left: 3px solid transparent;
    margin: 0;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
    border-left: 3px solid #007bff;
    transform: translateX(5px);
}

/* Ensure the sidebar has proper z-index */
.sidebar {
    z-index: 999;
}

/* Adjust main content z-index */
.content-box {
    z-index: 1;
}

@media screen and (max-width: 768px) {
    .dropdown-menu {
        position: static;
        box-shadow: none;
        margin-left: 20px;
        width: 100%;
        padding: 10px;
    }

    .dropdown-toggle .fa-chevron-down {
        transform: rotate(0);
        transition: transform 0.3s ease;
    }

    .dropdown:hover .fa-chevron-down {
        transform: rotate(180deg);
    }

    .dropdown-menu a {
        padding: 12px 20px;
        font-size: 1.2rem;
        font-weight: 700;
        width: 100%;
        display: flex;
        align-items: center;
        margin: 8px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-radius: 6px;
    }

    .dropdown-toggle {
        width: 100%;
        padding: 12px 20px;
        font-size: 1.2rem;
        font-weight: 700;
    }

    .dropdown-menu a i {
        margin-right: 10px;
        font-size: 1.4rem;
    }
}






.payment-history-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
    padding: 20px;
}

.payment-history-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    position: relative;
    overflow-y: auto;
}

.payment-records {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.payment-record {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.payment-amount {
    font-size: 1.4rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

.payment-date {
    color: #666;
    font-size: 1.1rem;
}

.payment-id {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

.payment-method {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

@media screen and (max-width: 768px) {
    .payment-history-content {
        width: 100%;
        margin: 20px;
        padding: 20px;
    }

    .payment-amount {
        font-size: 1.2rem;
    }

    .payment-date {
        font-size: 1rem;
    }
}





@media screen and (max-width: 768px) {
    .student-info-content {
        width: 95%;
        margin: 10px;
        padding: 15px;
    }

    .payment-table {
        font-size: 0.9rem;
    }

    .payment-table th,
    .payment-table td {
        padding: 8px;
    }

    .amount-badge {
        padding: 4px 8px;
    }
}

/* Add these styles to your existing styles */
.action-box.disabled {
    opacity: 0.8;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
}

.btn-disabled {
    background-color: #cccccc !important;
    cursor: not-allowed !important;
    color: #666666 !important;
}

.status-badge {
    display: inline-block;
    font-size: 12px;
    padding: 2px 5px;
    background-color: #f44336;
    color: white;
    border-radius: 3px;
    margin-left: 5px;
}

.alert {
    padding: 15px;
    margin: 15px;
    border-radius: 5px;
    position: relative;
}

.alert-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

    </style>
</head>
<body>
    <nav>
        <img src="img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading">DBTI Online Registration</div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="../../public/index.php">Home</a></li>
            <li><a href="../../public/about.php">About</a></li>
            <li><a href="student_dashboard.php">Dashboard</a></li>
            <li><a href="logout.php">Logout</a></li>
        </ul>
    </nav>
    <?php if(isset($_SESSION['message'])): ?>
        <div class="alert alert-info">
            <?php
                echo $_SESSION['message'];
                unset($_SESSION['message']); // Clear the message after displaying
            ?>
        </div>
    <?php endif; ?>
    <div class="sidebar">
        <a href="#" onclick="openStudentInfo()"><i class="fa-solid fa-circle-info"></i> My Information</a>
        <hr>
        <a href="#" onclick="openPaymentHistory()"><i class="fa-solid fa-clock-rotate-left"></i> Payment History</a>
        <hr>
        <?php if ($total_paid >= 8600) { ?>
            <a href="#" class="disabled"><i class="fa-regular fa-credit-card"></i> Pay (Full Payment Completed)</a>
        <?php } else { ?>
            <div class="dropdown">
                <a href="#" class="dropdown-toggle">
                    <i class="fa-regular fa-credit-card"></i> Pay <i class="fas fa-chevron-down"></i>
                </a>
                <div class="dropdown-menu">
                    <a href="yearly.php"><i class="fas fa-calendar-year"></i> Pay Full Year</a>
                    <a href="choose_payment.php"><i class="fas fa-calendar-alt"></i> Pay Semestral</a>
                    <a href="quarterly.php"><i class="fas fa-clock"></i> Pay Quarterly</a>
                </div>
            </div>
        <?php } ?>
        <hr>
        <div class="action-box <?php echo ($student_data['registration_status'] === 'registered') ? 'disabled' : ''; ?>">
            <div class="action-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="action-details">
                <h3>Course Registration</h3>
                <p>Register for your courses</p>
                <?php if ($has_paid): ?>
                    <?php if ($student_data['registration_status'] === 'registered'): ?>
                        <a href="#" class="btn btn-disabled">Register <span class="status-badge">(Already Registered)</span></a>
                    <?php else: ?>
                        <a href="../../public/registration.php" class="btn btn-success">Register</a>
                    <?php endif; ?>
                <?php else: ?>
                    <button class="btn btn-disabled">Register (Payment Required)</button>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="content-box">
        <h2>Welcome, <?php echo htmlspecialchars($user['username']); ?></h2>
        <div class="payment-info">
            <h3>Payment Information</h3>
            <p>Total Amount Paid: K<?php echo number_format($total_paid, 2); ?></p>
            <p>Minimum Required Fee: K<?php echo number_format($required_fee, 2); ?></p>
            <p>Status:
                <?php echo $has_paid ? "Paid" : "Not Paid (Please complete your payment to register)"; ?>
            </p>
        </div>
    </div>

    <footer>
        <div class="footer">
            <span>
                Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
            </span>
        </div>
    </footer>
    <script>
    // Hamburger menu toggle
    function toggleNavbar() {
        const navbar = document.getElementById('navbar');
        navbar.classList.toggle('active');
    }

    // My Information popup
    function openStudentInfo() {
        fetch('get_student_info.php')
            .then(response => response.json())
            .then(data => {
                const modal = document.createElement('div');
                modal.className = 'student-info-modal';
                modal.innerHTML = `
                    <div class="student-info-content">
                        <span class="close-btn" onclick="this.parentElement.parentElement.remove()">×</span>
                        <h2>Student Information</h2>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">Student ID:</span>
                                <span class="value">${data.student_id}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">First Name:</span>
                                <span class="value">${data.first_name}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Last Name:</span>
                                <span class="value">${data.last_name}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Gender:</span>
                                <span class="value">${data.gender}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Phone Number:</span>
                                <span class="value">${data.phone_number}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Residential Address:</span>
                                <span class="value">${data.residential_address}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Home Province:</span>
                                <span class="value">${data.home_province}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Guardian Name:</span>
                                <span class="value">${data.guardian_name}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Guardian Occupation:</span>
                                <span class="value">${data.guardian_occupation}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Guardian Phone:</span>
                                <span class="value">${data.guardian_phone_number}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Guardian Email:</span>
                                <span class="value">${data.guardian_email}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Student Email:</span>
                                <span class="value">${data.student_email}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Date of Birth:</span>
                                <span class="value">${data.dob}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Program:</span>
                                <span class="value">${data.program}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Technology:</span>
                                <span class="value">${data.tech}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Payment Status:</span>
                                <span class="value">${data.payment_status}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Registration Status:</span>
                                <span class="value">${data.registration_status}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Year Level:</span>
                                <span class="value">${data.year_level}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Registration Date:</span>
                                <span class="value">${data.registration_timestamp}</span>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            });
    }

    // Payment dropdown toggle
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggle = document.querySelector('.dropdown-toggle');
        const dropdownMenu = document.querySelector('.dropdown-menu');

        dropdownToggle.addEventListener('click', function(e) {
            e.preventDefault();
            dropdownMenu.classList.toggle('show');
        });
    });

    // Payment History popup
    function openPaymentHistory() {
        const modal = document.createElement('div');
        modal.className = 'student-info-modal';
        modal.innerHTML = `
            <div class="student-info-content">
                <span class="close-btn" onclick="this.parentElement.parentElement.remove()">×</span>
                <iframe src="payment_history.php" style="width: 100%; height: 100%; border: none;"></iframe>
            </div>
        `;
        document.body.appendChild(modal);
    }
    </script>
    </body>
    </html>
<?php
// End output buffering and flush the output
ob_end_flush();
?>

