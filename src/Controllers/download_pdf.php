<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration and database connection
require_once '../../config/config.php';
require_once '../../config/db_conn.php';

// Security check - make sure user is logged in with appropriate role
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'registrar' && $_SESSION['role'] !== 'student')) {
    http_response_code(403);
    die("Unauthorized Access");
}

// Get student ID from URL
$studentId = isset($_GET['id']) ? $_GET['id'] : '';

// Check if PDF path is stored in session
if (empty($studentId) || !isset($_SESSION['pdf_download']) || !isset($_SESSION['pdf_name'])) {
    die("No PDF available for download.");
}

$pdf_path = $_SESSION['pdf_download'];
$pdf_name = $_SESSION['pdf_name'];

// Verify the PDF exists
if (!file_exists($pdf_path)) {
    die("PDF file not found. It may have expired or been deleted.");
}

// Send appropriate headers for PDF download
header('Content-Description: File Transfer');
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . $pdf_name . '"');
header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($pdf_path));

// Clear output buffer
ob_clean();
flush();

// Read file and output it
readfile($pdf_path);

// Clean up - remove the PDF file after download
// Comment this out if you want to keep the file for debugging
unlink($pdf_path);

// Clear the session variables
unset($_SESSION['pdf_download']);
unset($_SESSION['pdf_name']);

exit;